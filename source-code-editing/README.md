# Source code editing and HTML support

CKEditor can be set to accept any HTML element, attribute, style, or class. Additionally, the Source button allows users to edit raw HTML.

CKEditor 5 supports custom HTML markup and styles. Switch to the source mode to check out the HTML source of the content and play with it. See `time`, `abbr`, `aside` and `details` semantic elements as well as non-semantic tags and inline styles used to implement column layout.

See this demo live at [ckeditor.com](https://ckeditor.com/ckeditor-5/demo/html-support/) or read more about this feature in the [CKEditor 5 documentation](https://ckeditor.com/docs/ckeditor5/latest/features/general-html-support.html).

## Installation steps

1. Clone this repository:

```shell
<NAME_EMAIL>:ckeditor/ckeditor5-demos.git
```

2. Change the directory and install the dependencies:

```shell
cd ckeditor5-demos/source-code-editing && yarn
```

3. Open the `source-code-editing/index.js` file and update the values of the `LICENSE_KEY`, `CKBOX_TOKEN_URL`, and `WEB_SPELL_CHECKER_LICENSE_KEY` variables. Without this, the CKBox, spell checker, and other premium features will not be enabled.

4. Start the demo:


```shell
yarn dev
```
