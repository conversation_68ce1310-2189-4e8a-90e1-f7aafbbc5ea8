{"name": "ckeditor5-demos-e2e-tests", "private": true, "version": "0.1.0", "author": "CKSource (http://cksource.com/)", "license": "GPL-2.0-or-later", "scripts": {"prepare": "npx husky", "lint": "eslint \"**/*.{js,ts,jsx,tsx}\"", "build": "yarn workspaces run build", "start": "http-server ./builds --port 9001 --silent -c-1", "cy:open": "cypress open", "cy:test-demos": "cypress run --spec cypress/e2e/test_demos.cy.js --headless --browser chrome"}, "devDependencies": {"cypress": "^13.2.0", "eslint": "^8.57.1", "eslint-config-ckeditor5": "^7.1.0", "eslint-plugin-react": "^7.36.1", "http-server": "^14.1.1", "husky": "^9.1.6", "lint-staged": "^15.2.10"}, "workspaces": ["*"], "eslintIgnore": ["**/coverage", "**/builds", "**/dist", "**/node_modules"], "lint-staged": {"**/*.{js,ts,jsx,tsx}": ["eslint --quiet"]}}