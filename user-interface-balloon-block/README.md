# Editor with Balloon Block user interface

CKEditor comes with a variety of editor types and user interface configuration options you can choose from. See all of them in action.

The Balloon Block editor offers the balloon editor with an extra block toolbar which can be accessed using the button attached to the editable content area and following the selection in the document. The toolbar gives access to additional block–level editing features.

See this demo live at [ckeditor.com](http://ckeditor.com/ckeditor-5/demo/editor-types.html#balloon-block) or read more about it in the [CKEditor 5 documentation](https://ckeditor.com/docs/ckeditor5/latest/examples/builds/balloon-block-editor.html).

## Installation steps

1. Clone this repository:

```shell
<NAME_EMAIL>:ckeditor/ckeditor5-demos.git
```

2. Change the directory and install the dependencies:

```shell
cd ckeditor5-demos/user-interface-balloon-block && yarn
```

3. Open the `user-interface-balloon-block/index.js` file and update the values of the `LICENSE_KEY` variable. Without this, the premium features will not be enabled.

4. Start the demo:

```shell
yarn dev
```
