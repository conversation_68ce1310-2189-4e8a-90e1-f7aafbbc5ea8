# Editor with Balloon user interface

CKEditor comes with a variety of editor types and user interface configuration options you can choose from. See all of them in action.

The Balloon editor comes with a floating toolbar that appears in a balloon once you select part of the content you want to edit. Balloon editor allows you to edit content in its real location on the web page instead of doing it in a separate administration section.

See this demo live at [ckeditor.com](http://ckeditor.com/ckeditor-5/demo/editor-types.html#balloon) or read more about it in the [CKEditor 5 documentation](https://ckeditor.com/docs/ckeditor5/latest/examples/builds/balloon-editor.html).

## Installation steps

1. Clone this repository:

```shell
<NAME_EMAIL>:ckeditor/ckeditor5-demos.git
```

2. Change the directory and install the dependencies:

```shell
cd ckeditor5-demos/user-interface-balloon && yarn
```

3. Open the `user-interface-balloon/index.js` file and update the values of the `LICENSE_KEY` variable. Without this, the premium features will not be enabled.

4. Start the demo:

```shell
yarn dev
```
