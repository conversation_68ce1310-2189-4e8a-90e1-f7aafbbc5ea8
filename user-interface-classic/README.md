# Editor with classic user interface

CKEditor comes with a variety of editor types and user interface configuration options you can choose from. See all of them in action.

The Classic editor offers a toolbar with an editing area placed in a specific position on the page. The toolbar stays visible when you scroll down the page, and the editor grows automatically with the content.

See this demo live at [ckeditor.com](http://ckeditor.com/ckeditor-5/demo/editor-types.html#classic) or read more about it in the [CKEditor 5 documentation](https://ckeditor.com/docs/ckeditor5/latest/examples/builds/classic-editor.html).

## Installation steps

1. Clone this repository:

```shell
<NAME_EMAIL>:ckeditor/ckeditor5-demos.git
```

2. Change the directory and install the dependencies:

```shell
cd ckeditor5-demos/user-interface-classic && yarn
```

3. Open the `user-interface-classic/index.js` file and update the values of the `LICENSE_KEY` variable. Without this, the premium features will not be enabled.

4. Start the demo:

```shell
yarn dev
```
