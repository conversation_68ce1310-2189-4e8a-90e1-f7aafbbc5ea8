# Markdown editor

The Markdown plugin enables the editor to support both Markdown input and output.

Use the Source button to edit and paste raw Markdown or simply type your favorite MD shortcuts in the WYSIWYG mode.

See this demo live at [ckeditor.com](http://ckeditor.com/ckeditor-5/demo/markdown/) or read more about this feature in the [CKEditor 5 documentation](https://ckeditor.com/docs/ckeditor5/latest/features/markdown.html).

## Installation steps

1. Clone this repository:

```shell
<NAME_EMAIL>:ckeditor/ckeditor5-demos.git
```

2. Change the directory and install the dependencies:

```shell
cd ckeditor5-demos/markdown && yarn
```

3. Open the `markdown/index.js` file and update the value of the `LICENSE_KEY` variables. Without this, the premium features will not be enabled.

4. Start the demo:

```shell
yarn dev
```
