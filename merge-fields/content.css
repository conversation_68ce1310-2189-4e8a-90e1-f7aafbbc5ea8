/* ---- Cross-editor content styles. --------------------------------------------------------------- */
@import url("https://fonts.googleapis.com/css2?family=Source+Serif+4:ital,opsz,wght@0,8..60,200..900;1,8..60,200..900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Source+Sans+3:ital,wght@0,200..900;1,200..900&display=swap");

:root {
	--font-family: Mulish, system-ui, -apple-system, sans-serif;
	--font-family-italic: MulishItalic, system-ui, -apple-system, sans-serif;
	--font-family-mono: Consolas, Monaco, "Andale Mono", "Ubuntu Mono",
		ui-monospace, monospace;
	--body-line-height: 1.4;
	--dense-text-line-height: 1.55;
	--headline-line-height: 1.1;
	--color-seal-1: hsl(0, 0%, 100%);
	--color-seal-2: hsl(228, 100%, 98%);
	--color-seal-3: hsl(227, 66%, 95%);
	--color-tang-hs: 263, 59%;
	--color-tang-4: hsl(var(--color-tang-hs), 92%);
	--color-tang-5: hsl(var(--color-tang-hs), 52%);
	--color-tang-6: hsl(var(--color-tang-hs), 40%);
	--color-tang-7: hsl(var(--color-tang-hs), 26%);
	--color-tang-8: hsl(var(--color-tang-hs), 10%);
	--color-shark-0: hsl(0, 0%, 2%);
	--color-shark-1: hsl(219, 100%, 10%);
	--color-shark-2: hsl(214, 4%, 36%);
	--color-shark-3: hsl(213, 21%, 70%);
	--color-shark-4: hsl(210, 22%, 91%);
	--color-shark-5: hsl(210, 17%, 98%);
	--color-comet-hs: 77, 99%;
	--color-comet-3: hsl(var(--color-comet-hs), 78%);
	--color-comet-5: hsl(var(--color-comet-hs), 63%);
	--color-comet-6: hsl(var(--color-comet-hs), 45%);
	--color-comet-7: hsl(var(--color-comet-hs), 35%);
	--color-reef-1: hsl(189, 57%, 91%);
	--color-reef-2: hsl(16, 100%, 96%);
	--color-reef-3: hsl(201, 83%, 95%);
	--color-reef-4: hsl(234, 100%, 96%);
	--color-reef-5: hsl(78, 52%, 94%);
	--color-reef-6: hsl(345, 60%, 96%);
	--color-tropical-1: hsl(53, 100%, 43%);
	--color-tropical-2: hsl(36, 100%, 50%);
	--color-tropical-3: hsl(17, 100%, 57%);
	--color-tropical-4: hsl(346, 100%, 57%);
	--color-tropical-5: hsl(328, 100%, 49%);
	--color-tropical-6: hsl(307, 93%, 41%);
	--color-tropical-7: hsl(265, 90%, 51%);
	--color-arctic-1: hsl(208, 100%, 18%);
	--color-arctic-2: hsl(200, 100%, 27%);
	--color-arctic-3: hsl(188, 100%, 31%);
	--color-arctic-4: hsl(168, 100%, 36%);
	--color-arctic-5: hsl(140, 72%, 55%);
	--transition-time-1: 0.2s;
	--transition-time-2: 0.35s;
	--gradient-1: linear-gradient(122.1deg, var(--color-tang-8) 32.29%, var(--color-tang-7) 70.55%);
	--gradient-promo: linear-gradient(130deg, var(--color-comet-6), var(--color-tropical-1), var(--color-tropical-2), var(--color-tropical-3), var(--color-tropical-4), var(--color-tropical-5), var(--color-tropical-6), var(--color-tang-6));
	/* made at https://learnui.design/tools/gradient-generator.html */
	--z-index-cookies: 50;
	--z-index-popup: 100;
	--z-index-header: 2000;
	--z-index-skip-link: 2100;
	--z-index-chat: 2500;
	--z-index-overlay: 3000;
	--z-index-lightbox: 4000;
	--header-height: 5rem;
	--scroll-top: calc(var(--header-height) + var(--space-s));
	--step-0: 16px;
	--step--1: 12px;
	--headline-line-height: 1.4;
}

.ck-config-layers-stratosphere {
	--z-index-header: 12000;
	--z-index-chat: 12500;
	--z-index-overlay: 13000;
	--z-index-lightbox: 14000;
}

.ck-theme-aqua {
	--color-tang-hs: 215, 69%;
	--color-comet-hs: 55, 100%;
}

.ck-theme-turquoise {
	--color-tang-hs: 163, 30%;
	--color-comet-hs: 60, 89%;
}

*,
*::before,
*::after {
	box-sizing: border-box;
}

* {
	margin: 0;
}

input,
button,
textarea,
select {
	font: inherit;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
	overflow-wrap: break-word;
}

/*
Mulish subsetted for English, Polish & Turkish using gfonts `text` variable
Stylesheet link for current version from google: https://fonts.googleapis.com/css2?family=Mulish:ital,wght@0,300..800;1,300..800&&
*/
@font-face {
	font-style: normal;
	font-weight: 300 800;
	src: url(https://fonts.gstatic.com/l/font?kit=1Ptvg83HX_SGhgqU3QAqQqFdk_S_53g2xIaoNp0VDhtw7-GVUBN0tDQ-67CvTk-woiU_UymhWc6uZamsR35zmHRfyzWjoiw7o7kw5Cy088v_cJioRmLZRm08KDflvUOWty8hPgdtcVu4hVhYfQmRqWPaBdO7adZNQsVhb8oToLK7UG8_MTWYxyrFShMhBKmnzy0XyKKLH_Q89uZrWu2rYhhnLhdKxg2CnmYwqGxijs-n0LoDdxSPkwRgDJeMpUxhk0hQbBsPKJ4JCP74b5nykcZ4rGDnPGwm_iO5USqol9uqm7_w7A7PhqgZow2jCK7oqkW1plEbhX5jrnvG_MPQMBhuARiHDaK_XQ3he9wD0aM_oth0NaNtLhFM45mYLBU49w_1yGsCUVeHq53m8C8MbLFK7HhqcRtbBEdWFQZ28CTF4JAuATIfDt8aGg6pa8CrZb4sNxWxr-80J1zQ6I6h86FlZh1Xg1G6O8ZcnnHI8IP0oAZ13cP-OADgm4HKZjYO27dCopWJxswwZD6gOJ2_7WOW0ybWci8ZYN9V27q-6WMuxz2nDekyW2m0aogrgmOlG7d05z0GdLiYzdUsRpEHDl3BosSq7QtHjd3bAC4a1wqeZmLSAxJYEoy0C8nnXOEftfYU9fkFttnbZc5iLXLxcGwMRFWOzMZRSa4M2xd9Mv1B59HwMgtdBeZwS5CKa4HcUVsdb4r4qQZYXe3jvqMFgNdSQ6Q3VqSO1KI&skey=9f5b077cc22e75c7&v=v13) format("woff2-variations");
	font-family: "Mulish";
	font-display: swap;
}

@font-face {
	font-style: italic;
	font-weight: 300 800;
	src: url(https://fonts.gstatic.com/l/font?kit=1Pttg83HX_SGhgqk2gotYKNnBcIIwXowwoSqOJMXDB127eOLThF2sjI86b6hTE22pCc9LVejW8ioZ6uiSXxxnnJdySu9oC49pbsy6iK28c35cpqWeGDbQGs-Kjnrv0GQsS0jIBlvc12-h1pWcwuTr2XYug-7aNZLQshhbsoRoLe7Sm8zMTaZ_irAShohGamizyAXz6K1H_E8_-Z2WuirbxhgKS9Kxw2Bnmcwt2xjjsin0bosdxWOowRjDJaMokx_k0lQaRsOKLgJC_75b578q8ZkrGbOIA5E_kDbUErKlbrImNmS6Gmtg8x7pWjBD8SKoi7Xrzl5jxcBpRWk8KyyPXQMD3XlAtDdTX6Daqxhw9Jdsa4WIdQPO2Uu9ez6O29a73SX0RNgSy7lsOOE7FBucc0o8gUIblk5JAQ0NEYU0mWnw9ZMJXV9K5t4PEvLTIrJTfVOHl3ThaZWDBKyxMHD3u0HSFA1rAPYC5U-ryGqwtKWk1AX6ZScDVSCrdSoUWxs4-wgm83r_JVSX2DCBMLd0D_07Xu0TE16Ybw229rd6gJNxVvECI5RXw3Xbe1IhAnGEtwX71Vlf9H7x7tPS_5kAjGiranJ43kknK64EF55xHv9dBSxFmU7BvjXHLyESpt8rI137YFmraC4f7ABMA2SbBBvWyjt0oQyaO1v-1ceEbwixZeTF0w-IaITbNXpTcu_eBB-R8Kbgk87d6OAk-xmrJsxbOlUe-ru_OIZlQ&skey=12f3c822011b7b1c&v=v13) format("woff2-variations");
	font-family: "MulishItalic";
	font-display: swap;
}

html,
body {
	height: auto;
	min-height: 100%;
	scroll-behavior: smooth;
}

body {
	line-height: var(--body-line-height);
	-webkit-font-smoothing: antialiased;
	background: var(--color-seal-1);
	color: var(--color-shark-1);
	font-size: var(--step-0);
	font-family: var(--font-family);
	background-color: transparent;
	padding: 20mm 2mm !important;
}

html.ck-config-no-scroll,
html.ck-config-no-scroll body {
	scroll-behavior: initial;
}

body.is-hidden {
	overflow: hidden;
}

ol[role=list],
ul[role=list] {
	list-style: none;
}

[role=list] {
	padding: 0;
}

a {
	color: var(--a-color, var(--color-tang-5));
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a:focus,
button:focus {
	transition-duration: var(--transition-time-1);
	transition-property: box-shadow;
	outline: 0;
	box-shadow: 0 0 0 2px var(--color-seal-1), 0 0 0 4px var(--color-tang-5);
}

a:focus-visible,
button:focus-visible {
	transition-duration: var(--transition-time-1);
	transition-property: box-shadow;
	outline: 0;
	box-shadow: 0 0 0 2px var(--color-seal-1), 0 0 0 4px var(--color-tang-5);
}

a:has(sup) {
	-webkit-text-decoration: underline dashed currentColor 1px;
	text-decoration: underline dashed currentColor 1px;
}

a:focus:not(:focus-visible),
button:focus:not(:focus-visible) {
	box-shadow: none;
}

i,
em {
	font-style: italic;
	font-family: var(--font-family-italic);
}

fieldset {
	border: 0;
	padding: 0;
}

code:not([class*=language-]) {
	border-radius: var(--space-4xs);
	background-color: var(--color-shark-4);
	padding: var(--space-4xs);
	color: var(--color-tropical-4);
	font-size: 90%;
}

kbd {
	border-style: groove;
	border-radius: var(--space-4xs);
	background: var(--color-shark-4);
	padding: 0 var(--space-4xs);
	font-size: var(--step--1);
	font-family: var(--font-family-mono);
}

img {
	height: auto;
}

[id] {
	scroll-margin-top: var(--scroll-top);
}

sup {
	padding: 4px;
	font-weight: 800;
}

h1,
h2,
h3 {
	line-height: var(--headline-line-height);
}

small {
	font-size: var(--step--1);
}

img,
picture,
video,
canvas,
svg {
	display: block;
	max-width: none;
}

li, p {
	max-width: var(--max-ch, none);
}

p,
ul li:last-child,
ol li:last-child,
blockquote,
blockquote p,
pre {
	--ck-spacing-unit: 0.6em;
	--ck-spacing-large: calc(var(--ck-spacing-unit) * 1.5);
	margin-bottom: var(--ck-spacing-large);
}

blockquote:not([class]) {
	max-width: var(--max-ch, none);
}

h1 {
	font-size: 2em;
}

h2 {
	font-size: 1.5em;
}

h3 {
	font-size: 1.17em;
}

h4 {
	font-size: 1em;
}

h5 {
	font-size: 0.83em;
}

h6 {
	font-size: 0.67em;
}


/* Make sure all content containers are distinguishable on a web page even of not focused. */
.ck.ck-content:not(:focus) {
	border: 1px solid var(--ck-color-base-border);
}

/* Fix for editor styles overflowing into comment reply fields */
.ck-comment__input .ck.ck-content {
	border: 0;
	padding: 0;
	min-height: unset;
}

.ck.ck-content:not(.ck-style-grid__button__preview):not(.ck-editor__nested-editable) {
	padding: 1em 1.5em;
	/* Make sure all content containers have some min height to make them easier to locate. */
	min-height: 300px;
}

/* Font sizes and vertical rhythm for common elements (headings, lists, paragraphs, etc.) */
.ck-content h1 {
	font-size: 2.3em;
}

.ck-content h2 {
	font-size: 1.84em;
}

.ck-content h3 {
	font-size: 1.48em;
}

.ck-content h4 {
	font-size: 1.22em;
}

.ck-content h5 {
	font-size: 1.06em;
}

.ck-content h6 {
	font-size: 1em;
}

.ck-content h1,
.ck-content h2,
.ck-content h3,
.ck-content h4,
.ck-content h5,
.ck-content h6 {
	margin-bottom: 0.4em;
	padding-top: 0.8em;
}

.ck-content blockquote,
.ck-content ol,
.ck-content p,
.ck-content ul {
	padding-top: 0.2em;
}

.ck-content blockquote,
.ck-content p,
.ck-content ol li:last-child,
.ck-content ul li:last-child {
	margin-bottom: var(--ck-spacing-large);
}

blockquote p:last-child {
	margin-bottom: 0;
}

.ck-content .ck-recomendations__header-text {
	font-family: "Source Sans 3", sans-serif;
	font-optical-sizing: auto;
	font-weight: 400;
	font-style: normal;
	font-size: 25px;
	line-height: 32px;
	margin: 0;
	padding: 0;
	z-index: 1;
}

.ck-content .ck-offer_paragraph {
	background: #DDD8CD;
	color: #2F2F2F;
	padding: 16px;

	font-family: "Source Sans 3", sans-serif;
	font-optical-sizing: auto;
	font-weight: 400;
	font-style: normal;
	font-size: 13px;
	line-height: 16px;

}

.ck-content .ck-offer-header {
	font-family: "Source Serif 4", serif;
	font-optical-sizing: auto;
	font-weight: 700;
	font-style: normal;
	font-size: 18px;
	line-height: 22.5px;
	padding-top: 0;
	margin-bottom: 10px;
}

.ck-content img, .ck-content picture {
	display: inline;
}
