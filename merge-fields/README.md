# Editor with Merge Fields

Merge fields are visually distinct placeholder elements, which you may put into the content to mark places, where real values should be inserted. This is perfect for creating document templates and other kinds of personalized content. The feature is highly customizable, it offers a preview mode, and integrates with our document import/export plugins - [Export to Word](https://ckeditor.com/docs/ckeditor5/latest/features/converters/export-word.html), [Export to PDF](https://ckeditor.com/docs/ckeditor5/latest/features/converters/export-pdf.html) and [Import from Word](https://ckeditor.com/docs/ckeditor5/latest/features/converters/import-word/import-word.html).

See this demo live at [ckeditor.com](https://ckeditor.com/ckeditor-5/demo/merge-fields ) or read more about it in the [CKEditor 5 documentation](https://ckeditor.com/docs/ckeditor5/latest/features/merge-fields.html).


## Installation steps

1. Clone this repository:

```shell
<NAME_EMAIL>:ckeditor/ckeditor5-demos.git
```

2. Change the directory and install the dependencies:

```shell
cd ckeditor5-demos/merge-fields && yarn
```

3. Start the demo:

```shell
yarn dev
```
