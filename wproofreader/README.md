# WProofreader editor

Use a multilingual spell and grammar checker to eliminate unnecessary typos and mistakes. Customize this tool to respect any relevant proper names.

See this demo live at [ckeditor.com](https://ckeditor.com/spellchecker/#demo-proofreader/) or read more about it in the [CKEditor 5 documentation](https://ckeditor.com/docs/ckeditor5/latest/features/spelling-and-grammar-checking.html).

## Installation steps

1. Clone this repository:

```shell
<NAME_EMAIL>:ckeditor/ckeditor5-demos.git
```

2. Change the directory and install the dependencies:

```shell
cd ckeditor5-demos/wproofreader && yarn
```

3. Open the `wproofreader/index.js` file and update the values of the `WEB_SPELL_CHECKER_LICENSE_KEY` variable. Without this, the spell checker will not be enabled.

4. Start the demo:

```shell
yarn dev
```
