# Paste-from Office Enhanced editor

<PERSON><PERSON> from Office Enhanced retains a wider range of formatting options compared to the default paste available in the editor.

See this demo live at [ckeditor.com](https://ckeditor.com/productivity-pack/paste-from-office-enhanced-demo/) or read more about it in the [CKEditor 5 documentation](https://ckeditor.com/docs/ckeditor5/latest/features/pasting/paste-from-office-enhanced.html).

## Installation steps

1. Clone this repository:

```shell
<NAME_EMAIL>:ckeditor/ckeditor5-demos.git
```

2. Change the directory and install the dependencies:

```shell
cd ckeditor5-demos/paste-from-office-enhanced && yarn
```

3. Open the `paste-from-office-enhanced/index.js` file and update the value of the `LICENSE_KEY` variables. Without this, the enhanced Paste from Office feature will not be enabled.

4. Start the demo:

```shell
yarn dev
```
