<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<title>Editor with Bottom Toolbar user interface</title>
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<link rel="stylesheet" href="demo.css" />
		<link rel="stylesheet" href="content.css" />
	</head>

	<body>
		<div class="header-wrapper">
			<h1>Editor with Bottom Toolbar user interface</h1>
		</div>
		<div class="editor-wrapper">
			<div id="cke5-user-interface-bottom-toolbar-demo">
				<div id="cke5-user-interface-bottom-toolbar-demo-content">
					<div id="editor-content">
						<p>Hi <PERSON><PERSON><PERSON>,</p>
						<p>I think I found the right editor for our application! 🎉</p>
						<p>
							It has
							<strong>tons of features</strong>
							and a
							<strong>fantastic API</strong>
							to add customizations that we need.
							<a
								target="_blank"
								rel="noopener noreferrer"
								href="https://ckeditor.com/"
								>Check it out</a
							>
							and let’s discuss it on the catchup call tomorrow.
						</p>
						<p>
							Cheers,
							<br />
							Emma
						</p>
						<hr />
						<p>
							<img
								class="image_resized image-style-align-left"
								style="width: 65px"
								src="https://ckeditor.com/assets/images/ckdemo/editor-types/fabulous.png"
								alt="Fabulous Dummy App logo"
							/>
							<a
								target="_blank"
								rel="noopener noreferrer"
								href="https://fabulousdummyapp.com/"
							>
								<span style="color: hsl(0, 0%, 60%)">
									<strong>Fabulous Dummy App</strong>
								</span>
							</a>
							<br />
							<span style="color: hsl(0, 0%, 60%)">
								One app that will do
								<i>anything</i>
								you want.
							</span>
						</p>
						<p>
							<span style="color: hsl(0, 0%, 60%)"
								>2776 Black Oak Hollow Road, San Jose, CA</span
							>
						</p>
					</div>
				</div>
				<div
					id="cke5-user-interface-bottom-toolbar-demo-toolbar-container"
				></div>
			</div>
		</div>

		<script type="module" src="index.js"></script>
	</body>
</html>
