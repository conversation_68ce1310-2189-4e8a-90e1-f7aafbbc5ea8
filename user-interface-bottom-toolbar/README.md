# Editor with Bottom Toolbar user interface

CKEditor comes with a variety of editor types and user interface configuration options you can choose from. See all of them in action.

The Bottom configuration moves the main toolbar to the bottom of the editing window. This is often applied in email applications, (forum) post editors, chats, or instant messaging, where text creation comes first and formatting is applied occasionally.

See this demo live at [ckeditor.com](http://ckeditor.com/ckeditor-5/demo/editor-types.html#bottom-toolbar) or read more about it in the [CKEditor 5 documentation](https://ckeditor.com/docs/ckeditor5/latest/examples/builds-custom/bottom-toolbar-editor.html).

## Installation steps

1. Clone this repository:

```shell
<NAME_EMAIL>:ckeditor/ckeditor5-demos.git
```

2. Change the directory and install the dependencies:

```shell
cd ckeditor5-demos/user-interface-bottom-toolbar && yarn
```

3. Open the `user-interface-bottom-toolbar/index.js` file and update the values of the `LICENSE_KEY` variable. Without this, the premium features will not be enabled.

4. Start the demo:

```shell
yarn dev
```
