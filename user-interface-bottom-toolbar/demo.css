/* ---- Basic CSS reset ------------------------------------------------------ */
*, ::after, ::before {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
}

body,
html {
  margin: 0;
  padding: 0;
}

.ck.ck-content.ck-editor__editable_inline > :first-child {
  margin-top: 0;
}

/* ---- Styles of the demo page ------------------------------------------------------ */
.editor-wrapper {
  max-width: 66rem;
  margin: 0 auto 2rem auto;
}

.header-wrapper {
  padding: 1rem 2rem;
}

/* ---- Editor with bottom toolbar UI styles ------------------------------------------------------------------- */
#cke5-editor-types-demo-bottom-toolbar {
  display: flex;
  flex-direction: column;
}

#cke5-editor-types-demo-bottom-toolbar-content {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

#cke5-editor-types-demo-bottom-toolbar-content:not(.ck-focused) {
  border-color: var(--ck-color-base-border);
}

#cke5-editor-types-demo-bottom-toolbar-toolbar-container > .ck.ck-toolbar {
  border-top-width: 0;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}