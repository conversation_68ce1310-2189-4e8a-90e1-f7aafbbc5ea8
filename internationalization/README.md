# Internationalization editor

CKEditor offers a fully translated UI in over 40 languages, including Asian and RTL languages.

Set up the editor UI in your preferred language.

See this demo live at [ckeditor.com](http://ckeditor.com/ckeditor-5/demo/internationalization-editor/) or read more about this feature in the [CKEditor 5 documentation](https://ckeditor.com/docs/ckeditor5/latest/features/ui-language.html).

## Installation steps

1. Clone this repository:

```shell
<NAME_EMAIL>:ckeditor/ckeditor5-demos.git
```

2. Change the directory and install the dependencies:

```shell
cd ckeditor5-demos/internationalization && yarn
```

3. Start the demo:

```shell
yarn dev
```
