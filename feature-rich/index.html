<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<title>Feature-rich editor</title>
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<link rel="stylesheet" href="demo.css" />
		<link rel="stylesheet" href="content.css" />
	</head>

	<body>
		<div class="header-wrapper">
			<h1>Feature-rich editor</h1>
		</div>
		<div class="editor-wrapper">
			<div id="cke5-feature-rich-demo">
				<h1>Discover the riches of our editor ✨</h1>
				<p>
					Read on to better understand the functionalities you can test with
					this demo.
				</p>
				<h2>💡 Did you know that…</h2>
				<ul>
					<li>
						CKEditor is
						<strong>customizable</strong>. You can fine-tune the set of enabled
						plugins, available toolbar buttons, and the behavior of features.
					</li>
					<li>
						The editor supports
						<strong>@mentions</strong>. Start typing
						<code>@An</code>
						to see available suggestions.
					</li>
					<li>
						You can also insert dynamic placeholders within the content using <strong>merge fields</strong>! Start typing <code>{{</code> or use the 
						<img
							src="https://ckeditor.com/assets/images/ckdemo/feature-rich/merge-field-insert.png"
							srcset="
								https://ckeditor.com/assets/images/ckdemo/feature-rich/merge-field-insert.png 2x
							"
							alt="Insert merge field"
							width="20"
							height="20"
						/> button to insert them. Use the
						<img
							src="https://ckeditor.com/assets/images/ckdemo/feature-rich/merge-fields-preview.png"
							srcset="
								https://ckeditor.com/assets/images/ckdemo/feature-rich/merge-fields-preview.png 2x
							"
							alt="Preview merge field"
							width="20"
							height="20"
						/> button to preview values. {{mergeFieldExample}}
					</li>
					<li>
						You can
						<strong>paste content</strong>
						from Word or Google Docs, retaining the original document structure
						and formatting.
					</li>
					<li>
						Thanks to Import from Word
						<img
							src="https://ckeditor.com/assets/images/ckdemo/feature-rich/import_word.png"
							srcset="
								https://ckeditor.com/assets/images/ckdemo/feature-rich/import_word.png 2x
							"
							alt="Import from Word"
							width="20"
							height="20"
						/>, you can
						<strong>convert</strong>
						a DOCX document into HTML and edit it in CKEditor 5.
					</li>
					<li>
						You can
						<strong>export your document</strong>
						to PDF
						<img
							src="https://ckeditor.com/assets/images/ckdemo/feature-rich/export_pdf.png"
							srcset="
								https://ckeditor.com/assets/images/ckdemo/feature-rich/export_pdf.png 2x
							"
							alt="Export to PDF"
							width="20"
							height="20"
						/>
						or Word
						<img
							src="https://ckeditor.com/assets/images/ckdemo/feature-rich/export_word.png"
							srcset="
								https://ckeditor.com/assets/images/ckdemo/feature-rich/export_word.png 2x
							"
							alt="Export to Word"
							width="20"
							height="20"
						/>
						with a single click.
					</li>
					<li>
						This demo showcases
						<a
							target="_blank"
							rel="noopener noreferrer"
							href="https://ckeditor.com/ckbox/"
							>CKBox</a
						>
						<img
							src="https://ckeditor.com/assets/images/ckdemo/feature-rich/browse-files.png"
							srcset="
								https://ckeditor.com/assets/images/ckdemo/feature-rich/browse-files.png 2x
							"
							alt="Browse files"
							width="20"
							height="20"
						/>
						to
						<strong>manage images and other files</strong>. You can enable your
						own upload adapter instead.
					</li>
				</ul>
				<h2>🚀 Autoformatting in CKEditor 5</h2>
				<p>
					Some features of CKEditor 5 might be hard to spot at first glance.
					Thanks to
					<strong>autoformatting</strong>, you can use handy shortcuts in the
					editor to format the text on the fly:
				</p>
				<figure class="table" style="width: 75%">
					<table class="ck-table-resized">
						<colgroup>
							<col style="width: 23.03%" />
							<col style="width: 20.77%" />
							<col style="width: 56.2%" />
						</colgroup>
						<tbody>
							<tr>
								<td style="background-color: hsl(0, 0%, 90%)" rowspan="7">
									<strong>Block formatting</strong>
								</td>
								<td>Bulleted list</td>
								<td>
									Start a line with
									<code>*</code>
									or
									<code>-</code>
									followed by a space.
								</td>
							</tr>
							<tr>
								<td>Numbered list</td>
								<td>
									Start a line with
									<code>1.</code>
									or
									<code>1)</code>
									followed by a space.
								</td>
							</tr>
							<tr>
								<td>To-do list</td>
								<td>
									Start a line with
									<code>[ ]</code>
									or
									<code>[x]</code>
									followed by a space to insert an unchecked or checked list
									item.
								</td>
							</tr>
							<tr>
								<td>Headings</td>
								<td>
									Start a line with
									<code>#</code>, <code>##</code>, or
									<code>###</code>
									followed by a space to create a heading 1, heading 2, or
									heading 3.
								</td>
							</tr>
							<tr>
								<td>Block quote</td>
								<td>
									Start a line with
									<code>&gt;</code>
									followed by a space.
								</td>
							</tr>
							<tr>
								<td>Code block</td>
								<td>
									Start a line with
									<code>```</code>.
								</td>
							</tr>
							<tr>
								<td>Horizontal line</td>
								<td>
									Start a line with
									<code>---</code>.
								</td>
							</tr>
							<tr>
								<td style="background-color: hsl(0, 0%, 90%)" rowspan="4">
									<strong>Inline formatting</strong>
								</td>
								<td>
									<strong>Bold</strong>
								</td>
								<td>
									Type
									<code>**</code>
									or
									<code>__</code>
									around your text.
								</td>
							</tr>
							<tr>
								<td>
									<i>Italic</i>
								</td>
								<td>
									Type
									<code>*</code>
									or
									<code>_</code>
									around your text.
								</td>
							</tr>
							<tr>
								<td>
									<code>Code</code>
								</td>
								<td>
									Type
									<code>`</code>
									around your text.
								</td>
							</tr>
							<tr>
								<td>
									<s>Strikethrough</s>
								</td>
								<td>
									Type
									<code>~~</code>
									around your text.
								</td>
							</tr>
						</tbody>
					</table>
				</figure>
				<p>&nbsp;</p>
			</div>
		</div>

		<script src="https://cdn.ckbox.io/CKBox/2.0.0/ckbox.js"></script>
		<script type="module" src="index.js"></script>
	</body>
</html>
