/* ---- Basic CSS reset ------------------------------------------------------ */
*, ::after, ::before {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
}

body,
html {
  margin: 0;
  padding: 0;
}

.ck.ck-content.ck-editor__editable_inline > :first-child {
  margin-top: 0;
}

/* ---- Styles of the demo page ------------------------------------------------------ */
.editor-wrapper {
  max-width: 66rem;
  margin: 0 auto 2rem auto;
}

.header-wrapper {
  padding: 1rem 2rem;
}

/* ---- Styles specific to the headless editor demo ------------------------------------------------------ */
.App {
  --ck-demo-space-2xs: clamp(0.38rem, calc(0.34rem + 0.18vw), 0.5rem);
  --ck-demo-space-xs: clamp(0.5rem, calc(0.43rem + 0.37vw), 0.75rem);
  --ck-demo-space-s: clamp(0.63rem, calc(0.51rem + 0.55vw), 1rem);
  --ck-demo-space-m: clamp(1.38rem, calc(1.19rem + 0.92vw), 2rem);
  --ck-demo-headless-primary-color: hsl(263, 59%, 52%);
  --ck-demo-headless-button-bg-color-on-hover: hsl(0, 0%, 93%) ;
}

.ck {
  --ck-color-base-border: hsl(210, 22%, 91%);
  --ck-border-radius: var(--ck-demo-space-2xs);
}

.ck.ck-content.ck-editor__editable_inline {
  --ck-border-radius: var(--ck-demo-space-xs);
}

.ck.ck-content.ck-editor__editable_inline:not(:focus) {
  border-color: var(--ck-color-base-border);
}

.editor-toolbar {
  position: sticky;
  top: var(--header-height);
  display: flex;
  flex-flow: row wrap;
  gap: var(--ck-demo-space-2xs);
  width: 100%;
  margin-bottom: var(--ck-demo-space-m);
  padding: var(--ck-demo-space-s);
  background: white;
  border: 1px solid var(--ck-color-base-border);
  border-radius: var(--ck-demo-space-xs);
  z-index: 1;
}

.custom-editor-button {
  display: inline-block;
  background-color: transparent;
  font-weight: 700;
  color: var(--ck-demo-headless-primary-color);
  border-radius: var(--ck-demo-space-2xs);
  text-decoration: none;
  text-align: center;
  border: 1px solid var(--ck-color-base-border);
  cursor: pointer;
  transition: background-color 0.15s;
}
.custom-editor-button--fill {
  color: white;
  background-color: var(--ck-demo-headless-primary-color);
  border-color: var(--ck-demo-headless-primary-color);
}
.custom-editor-button--disabled {
  opacity: 0.4;
  color: black;
  pointer-events: none;
}
.custom-editor-button:not(.custom-editor-button--disabled, .custom-editor-button--fill):hover {
  background-color: var(--ck-demo-headless-button-bg-color-on-hover);
}