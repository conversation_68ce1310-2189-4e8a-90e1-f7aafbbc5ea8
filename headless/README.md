# Headless editor

CKEditor can fit nicely with the rest of your application. Headless editor allows you to build your own UI on top of our editing engine, using your favorite technology. See our example variation.

Here is just one example of a (basic) UI built for CKEditor 5 on top of React. You can build your own UI in any other technology, using CKEditor as a headless rich text editing engine under the hood.

See this demo live at [ckeditor.com](http://ckeditor.com/ckeditor-5/demo/headless/) or read more about this feature in the [CKEditor 5 documentation](https://ckeditor.com/docs/ckeditor5/latest/examples/framework/custom-ui.html).

## Installation steps

1. Clone this repository:

```shell
<NAME_EMAIL>:ckeditor/ckeditor5-demos.git
```

2. Change the directory and install the dependencies:

```shell
cd ckeditor5-demos/headless && yarn
```

3. Start the demo:

```shell
yarn dev
```
