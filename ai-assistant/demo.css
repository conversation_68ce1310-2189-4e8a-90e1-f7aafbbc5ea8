/* ---- Customizations to the color grid palettes (font and background color) ------------------------------------------------------ */
.ck.ck-color-ui-dropdown {
  --ck-color-grid-tile-size: 20px;
}
.ck.ck-color-ui-dropdown .ck-color-grid {
  grid-gap: 1px;
}
.ck.ck-color-ui-dropdown .ck-color-grid .ck-button {
  border-radius: 0;
}
.ck.ck-color-ui-dropdown .ck-color-grid__tile:hover:not(.ck-disabled),
.ck.ck-color-ui-dropdown .ck-color-grid__tile:focus:not(.ck-disabled) {
  z-index: 1;
  transform: scale(1.3);
}

/* ---- Basic CSS reset ------------------------------------------------------ */
*, ::after, ::before {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
}

body,
html {
  margin: 0;
  padding: 0;
}

.ck.ck-content.ck-editor__editable_inline > :first-child {
  margin-top: 0;
}

/* ---- Styles of the demo page ------------------------------------------------------ */
.editor-wrapper {
  max-width: 66rem;
  margin: 0 auto 2rem auto;
}

.header-wrapper {
  padding: 1rem 2rem;
}

/* ---- Document editor demo styles ------------------------------------------------------------------- */
#cke5-ai-assistant-demo {
  position: relative;
  display: grid;
  grid-template-columns: 100%;
  grid-template-rows: 80vh;
}

#cke5-ai-assistant-demo .ck.ck-editor__main {
  height: calc(100% - 39px);
}

#cke5-ai-assistant-demo .ck-editor__editable.ck-content {
  overflow-y: auto;
  height: 100%;
}

.cke5-ai-assistant-demo__toolbar-container .ck.ck-toolbar__items {
  border: 0;
}

#cke5-ai-assistant-demo .ck.ck-reset.ck-editor.ck-rounded-corners {
  max-width: 100%;
  width: 100%;
}

.cke5-ai-assistant-demo__sidebar {
  background: var(--color-shark-4);
  padding: 0 var(--space-2xs) var(--space-2xs);
  overflow: hidden;
  border-style: solid;
  border-width: 1px 1px 1px 0;
  border-color: var(--ck-color-base-border);
  overflow-y: auto;
  position: relative;
  display: none;
}

@media screen and (min-width: 768px) {
  .cke5-ai-assistant-demo__sidebar {
    display: block;
  }
}