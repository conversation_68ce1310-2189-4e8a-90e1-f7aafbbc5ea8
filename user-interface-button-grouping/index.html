<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<title>Editor with <PERSON>ton Grouping user interface</title>
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<link rel="stylesheet" href="demo.css" />
		<link rel="stylesheet" href="content.css" />
	</head>

	<body>
		<div class="header-wrapper">
			<h1>Editor with Button Grouping user interface</h1>
		</div>
		<div class="editor-wrapper">
			<div id="cke5-user-interface-button-grouping-demo">
				<h1>Tidy things up 🧹️ with a customizable toolbar</h1>
				<p>
					CKEditor’s
					<a
						target="_blank"
						rel="noopener noreferrer"
						href="https://ckeditor.com/docs/ckeditor5/latest/features/toolbar/toolbar.html"
						>toolbar</a
					>
					lets you mix and match as many (or as few) features as you need. When
					things get too busy, you can
					<strong>easily group toolbar buttons</strong>. Look, for instance, how
					this editor’s features are divided into neat groups with
					<code>|</code>
					separators.
				</p>
				<p>
					To unclutter the toolbar even more, you can
					<strong>arrange items into dropdowns</strong>. Use the
					<strong>Lists</strong>
					button above to reveal a menu with different list types.
				</p>
				<h2>Outstanding configurability 👌</h2>
				<p>
					But that’s not all! CKEditor also offers
					<strong>toolbar wrapping</strong>
					: automatic or at a specified breakpoint. This works great with
					toolbars that do not fit the editor’s width. Additional buttons can
					either be hidden under a
					<code>⋮</code>
					menu or moved to the next toolbar line.
				</p>
				<p>
					You can even
					<strong>add your own buttons</strong>
					to the toolbar by
					<a
						target="_blank"
						rel="noopener noreferrer"
						href="https://ckeditor.com/docs/ckeditor5/latest/framework/guides/plugins/creating-simple-plugin-timestamp.html"
						>creating custom plugins</a
					>
					!
				</p>
			</div>
		</div>
		<script type="module" src="index.js"></script>
	</body>
</html>
