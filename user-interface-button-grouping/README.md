# Editor with Button Grouping user interface

CKEditor comes with a variety of editor types and user interface configuration options you can choose from. See all of them in action.

The Button Grouping option unclutters the main toolbar by merging buttons into expandable groups. This way you can keep the toolbar concise and well-organized, deciding which buttons should be grouped together.

See this demo live at [ckeditor.com](http://ckeditor.com/ckeditor-5/demo/editor-types.html#button-grouping) or read more about it in the [CKEditor 5 documentation](https://ckeditor.com/docs/ckeditor5/latest/features/toolbar/toolbar.html#grouping-toolbar-items-in-drop-downs-nested-toolbars).

## Installation steps

1. Clone this repository:

```shell
<NAME_EMAIL>:ckeditor/ckeditor5-demos.git
```

2. Change the directory and install the dependencies:

```shell
cd ckeditor5-demos/user-interface-button-grouping && yarn
```

3. Open the `user-interface-button-grouping/index.js` file and update the values of the `LICENSE_KEY` variable. Without this, the premium features will not be enabled.

4. Start the demo:

```shell
yarn dev
```
