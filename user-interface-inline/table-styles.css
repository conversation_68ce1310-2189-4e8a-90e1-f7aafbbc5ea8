/* 表格样式 - 用于渲染输出的内容 */

/* 基础表格样式 */
table {
  border-collapse: collapse;
  border-spacing: 0;
  width: 100%;
  margin: 1em 0;
  border: 1px solid #d1d5db;
  background-color: #ffffff;
}

/* 表格单元格样式 */
table td,
table th {
  min-width: 2em;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  text-align: left;
  vertical-align: top;
}

/* 表头样式 */
table th {
  font-weight: 600;
  background-color: #f9fafb;
  color: #374151;
}

/* 表格行样式 */
table tbody tr:nth-child(even) {
  background-color: #f9fafb;
}

table tbody tr:hover {
  background-color: #f3f4f6;
}

/* 表格标题样式 */
table caption {
  caption-side: bottom;
  padding: 8px;
  font-style: italic;
  color: #6b7280;
  text-align: center;
}

/* 响应式表格 */
@media (max-width: 768px) {
  table {
    font-size: 14px;
  }
  
  table td,
  table th {
    padding: 6px 8px;
  }
}

/* 表格边框样式变体 */
.table-bordered {
  border: 2px solid #374151;
}

.table-bordered td,
.table-bordered th {
  border: 1px solid #374151;
}

/* 无边框表格 */
.table-borderless {
  border: none;
}

.table-borderless td,
.table-borderless th {
  border: none;
}

/* 紧凑表格 */
.table-sm td,
.table-sm th {
  padding: 4px 8px;
}

/* 表格条纹样式 */
.table-striped tbody tr:nth-child(odd) {
  background-color: #f9fafb;
}

/* 表格悬停效果 */
.table-hover tbody tr:hover {
  background-color: #e5e7eb;
}

/* 深色主题表格 */
.table-dark {
  background-color: #1f2937;
  color: #f9fafb;
}

.table-dark th,
.table-dark td {
  border-color: #374151;
}

.table-dark th {
  background-color: #374151;
}

.table-dark tbody tr:nth-child(even) {
  background-color: #374151;
}

.table-dark tbody tr:hover {
  background-color: #4b5563;
}
