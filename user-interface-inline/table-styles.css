/* CKEditor5 表格样式支持 */

/* CKEditor5 自带的表格列调整功能样式 */
.ck-table-resized {
  /* 当用户拖拽调整列宽时，CKEditor5 会自动添加这个类 */
  table-layout: fixed;
}

.ck-table-resized colgroup {
  /* colgroup 用于保存列宽信息 */
}

.ck-table-resized colgroup col {
  /* 每一列的宽度通过 style 属性设置，例如：style="width: 25.5%" */
}

/* 确保 CKEditor5 的表格调整功能在输出内容中也能正常工作 */
table.ck-table-resized {
  table-layout: fixed;
  width: 100%; /* 默认宽度，可以被内联样式覆盖 */
}

table.ck-table-resized colgroup col[style*="width"] {
  /* 保持列宽设置 */
}

/* 基础表格样式 - 当没有 ck-table-resized 类时使用 */
table:not(.ck-table-resized) {
  border-collapse: collapse;
  border-spacing: 0;
  width: 100%;
  margin: 1em 0;
  border: 1px solid #d1d5db;
  background-color: #ffffff;
}

/* 表格单元格基础样式 */
table td,
table th {
  min-width: 2em;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  text-align: left;
  vertical-align: top;
}

/* 表头样式 */
table th {
  font-weight: 600;
  background-color: #f9fafb;
  color: #374151;
}

/* 表格行样式 */
table tbody tr:nth-child(even) {
  background-color: #f9fafb;
}

table tbody tr:hover {
  background-color: #f3f4f6;
}

/* 表格标题样式 */
table caption {
  caption-side: bottom;
  padding: 8px;
  font-style: italic;
  color: #6b7280;
  text-align: center;
}

/* 响应式表格 */
@media (max-width: 768px) {
  table {
    font-size: 14px;
  }
  
  table td,
  table th {
    padding: 6px 8px;
  }
}

/* 确保内联样式优先级 */
table[style] {
  /* 内联样式会自动覆盖这里的样式 */
}

table td[style],
table th[style] {
  /* 内联样式会自动覆盖这里的样式 */
}

/* 表格图形容器样式 */
figure.table {
  margin: 1em 0;
}

figure.table table {
  margin: 0;
}
