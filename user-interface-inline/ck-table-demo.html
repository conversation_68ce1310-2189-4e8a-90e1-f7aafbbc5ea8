<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CKEditor5 表格调整功能演示</title>
    <link rel="stylesheet" href="table-styles.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .demo-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .demo-section h3 {
            margin-top: 0;
            color: #007cba;
        }
        .code-example {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>CKEditor5 表格调整功能演示</h1>
    
    <div class="demo-section">
        <h3>📋 关于 ck-table-resized 类</h3>
        <p><strong>ck-table-resized</strong> 是 CKEditor5 自动添加的 CSS 类，当用户通过拖拽表格列边框调整列宽时会自动生成。</p>
        <ul>
            <li>✅ 这个类是 CKEditor5 内置功能，不需要手动添加</li>
            <li>✅ 当用户拖拽列边框时，编辑器会自动添加这个类</li>
            <li>✅ 同时会生成 &lt;colgroup&gt; 结构来保存列宽信息</li>
            <li>✅ 表格会使用 table-layout: fixed 布局</li>
        </ul>
    </div>
    
    <div class="demo-section">
        <h3>1. 普通表格（没有调整过列宽）</h3>
        <table>
            <thead>
                <tr>
                    <th>姓名</th>
                    <th>年龄</th>
                    <th>城市</th>
                    <th>职业</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>张三</td>
                    <td>25</td>
                    <td>北京</td>
                    <td>软件工程师</td>
                </tr>
                <tr>
                    <td>李四</td>
                    <td>30</td>
                    <td>上海</td>
                    <td>产品经理</td>
                </tr>
            </tbody>
        </table>
        <div class="code-example">
&lt;table&gt;
  &lt;thead&gt;
    &lt;tr&gt;
      &lt;th&gt;姓名&lt;/th&gt;
      &lt;th&gt;年龄&lt;/th&gt;
      &lt;th&gt;城市&lt;/th&gt;
      &lt;th&gt;职业&lt;/th&gt;
    &lt;/tr&gt;
  &lt;/thead&gt;
  ...
&lt;/table&gt;
        </div>
    </div>
    
    <div class="demo-section">
        <h3>2. 带 ck-table-resized 类的表格（模拟用户调整后的效果）</h3>
        <figure class="table" style="width: 80%;">
            <table class="ck-table-resized">
                <colgroup>
                    <col style="width: 25%;">
                    <col style="width: 15%;">
                    <col style="width: 20%;">
                    <col style="width: 40%;">
                </colgroup>
                <thead>
                    <tr>
                        <th>产品名称</th>
                        <th>价格</th>
                        <th>库存</th>
                        <th>描述</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>笔记本电脑</td>
                        <td>¥5999</td>
                        <td>50台</td>
                        <td>高性能办公笔记本，适合程序开发和设计工作</td>
                    </tr>
                    <tr>
                        <td>智能手机</td>
                        <td>¥2999</td>
                        <td>100台</td>
                        <td>最新款智能手机，拍照效果出色</td>
                    </tr>
                </tbody>
            </table>
        </figure>
        <div class="code-example">
&lt;figure class="table" style="width: 80%;"&gt;
  &lt;table class="ck-table-resized"&gt;
    &lt;colgroup&gt;
      &lt;col style="width: 25%;"&gt;
      &lt;col style="width: 15%;"&gt;
      &lt;col style="width: 20%;"&gt;
      &lt;col style="width: 40%;"&gt;
    &lt;/colgroup&gt;
    &lt;thead&gt;
      &lt;tr&gt;
        &lt;th&gt;产品名称&lt;/th&gt;
        &lt;th&gt;价格&lt;/th&gt;
        &lt;th&gt;库存&lt;/th&gt;
        &lt;th&gt;描述&lt;/th&gt;
      &lt;/tr&gt;
    &lt;/thead&gt;
    ...
  &lt;/table&gt;
&lt;/figure&gt;
        </div>
    </div>
    
    <div class="demo-section">
        <h3>3. 如何在 CKEditor5 中使用列调整功能</h3>
        <ol>
            <li><strong>插入表格</strong>：点击工具栏中的"插入表格"按钮</li>
            <li><strong>调整列宽</strong>：将鼠标悬停在表格列边框上，会出现调整光标</li>
            <li><strong>拖拽调整</strong>：按住鼠标左键拖拽来调整列宽</li>
            <li><strong>自动生成</strong>：CKEditor5 会自动添加 <code>ck-table-resized</code> 类和 <code>&lt;colgroup&gt;</code> 结构</li>
        </ol>
        
        <h4>🔧 必需的插件配置：</h4>
        <div class="code-example">
import { TableColumnResize } from 'ckeditor5';

// 在插件列表中添加
plugins: [
  // ... 其他插件
  Table,
  TableColumnResize,  // 👈 这个插件负责列调整功能
  TableProperties,
  TableCellProperties,
  // ...
]
        </div>
    </div>
    
    <div class="demo-section">
        <h3>4. CSS 样式说明</h3>
        <p>当表格有 <code>ck-table-resized</code> 类时：</p>
        <ul>
            <li><code>table-layout: fixed</code> - 使用固定表格布局</li>
            <li><code>&lt;colgroup&gt;</code> 中的列宽设置会生效</li>
            <li>每列的宽度通过 <code>style="width: XX%"</code> 设置</li>
        </ul>
        
        <div class="code-example">
.ck-table-resized {
  table-layout: fixed;
}

.ck-table-resized colgroup col {
  /* 列宽通过内联样式设置 */
}
        </div>
    </div>
    
    <p><strong>💡 提示：</strong>这个功能是 CKEditor5 的内置功能，当您的配置正确时，用户就可以通过拖拽来调整表格列宽，编辑器会自动处理所有的 HTML 结构和 CSS 类。</p>
</body>
</html>
