<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<title>Editor with Inline user interface</title>
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<link rel="stylesheet" href="demo.css" />
		<link rel="stylesheet" href="content.css" />
		<link rel="stylesheet" href="table-styles.css" />
	</head>

	<body>
		<div class="header-wrapper">
			<h1>Editor with Inline user interface</h1>
		</div>
		<div class="editor-wrapper">
			<div id="cke5-editor-types-demo-inline">
				<header id="inline-header">
					<h2>Gone traveling</h2>
					<h3>Monthly travel news and inspiration</h3>
				</header>

				<div id="inline-main">
					<h3>Destination of the Month</h3>

					<h4>Valletta</h4>

					<figure class="image image-style-align-right" style="width: 50%">
						<img
							alt="Picture of a sunlit facade of a Maltan building."
							src="https://ckeditor.com/assets/images/ckdemo/editor-types/malta.jpg"
							srcset="
								https://ckeditor.com/assets/images/ckdemo/editor-types/malta.jpg,
								https://ckeditor.com/assets/images/ckdemo/editor-types/malta_2x.jpg 2x
							"
						/>
						<figcaption>It's siesta time in Valletta.</figcaption>
					</figure>

					<p>
						The capital city of
						<a
							href="https://en.wikipedia.org/wiki/Malta"
							target="_blank"
							rel="external"
							>Malta</a
						>
						is the top destination this summer. It’s home to a cutting-edge
						contemporary architecture, baroque masterpieces, delicious local
						cuisine and at least 8 months of sun. It’s also a top destination
						for filmmakers, so you can take a tour through locations familiar to
						you from Game of Thrones, Gladiator, Troy and many more.
					</p>
				</div>

				<!-- <div class="demo-row">
					<div class="demo-row__half">
						<div id="inline-footer-first">
							<h3>The three greatest things you learn from traveling</h3>
							<p><a href="#">Find out more</a></p>
						</div>
					</div>

					<div class="demo-row__half">
						<div id="inline-footer-second">
							<h3>Walking the capitals of Europe: Warsaw</h3>
							<p><a href="#">Find out more</a></p>
						</div>
					</div>
				</div> -->
			</div>
		</div>

		<!-- 添加按钮和内容展示区域 -->
		<div class="controls-wrapper" style="margin-top: 30px; padding: 20px; border-top: 1px solid #ddd;">
			<h3>编辑器内容控制</h3>

			<!-- 表格使用说明 -->
			<div style="background-color: #f0f8ff; padding: 15px; border-radius: 5px; margin-bottom: 20px; border-left: 4px solid #007cba;">
				<h4 style="margin: 0 0 10px 0; color: #007cba;">📋 表格样式设置说明</h4>
				<p style="margin: 5px 0; font-size: 14px;"><strong>方法一：拖拽调整列宽</strong></p>
				<p style="margin: 5px 0; font-size: 14px;">• 将鼠标悬停在表格列边框上，出现调整光标时拖拽调整</p>
				<p style="margin: 5px 0; font-size: 14px;">• CKEditor5 会自动添加 <code>ck-table-resized</code> 类和列宽信息</p>
				<p style="margin: 5px 0; font-size: 14px;"><strong>方法二：属性面板设置</strong></p>
				<p style="margin: 5px 0; font-size: 14px;">• 右键点击表格 → "表格属性"：设置表格宽度、高度、边框、背景色等</p>
				<p style="margin: 5px 0; font-size: 14px;">• 右键点击单元格 → "单元格属性"：设置单元格宽度、高度、边框、背景色等</p>
				<p style="margin: 5px 0; font-size: 14px;">• 所有设置都会以内联样式保存，确保在任何环境下都能正确显示</p>
			</div>

			<button id="get-content-btn" style="padding: 10px 20px; background-color: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer; margin-bottom: 20px;">
				获取编辑器内容
			</button>

			<div class="content-display">
				<h4>编辑器内容展示：</h4>
				<div id="content-output" style="border: 1px solid #ccc; padding: 15px; min-height: 100px; background-color: #f9f9f9; border-radius: 4px;">
					点击上方按钮获取编辑器内容...
				</div>
			</div>
		</div>

		<script type="module" src="index.js"></script>
	</body>
</html>
