lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    devDependencies:
      ckeditor5:
        specifier: ^46.0.0
        version: 46.0.0
      ckeditor5-premium-features:
        specifier: ^46.0.0
        version: 46.0.0(ckeditor5@46.0.0)
      vite:
        specifier: ^5.0.0
        version: 5.4.19(@types/node@24.1.0)

packages:

  '@aws-crypto/crc32@5.2.0':
    resolution: {integrity: sha512-nLbCWqQNgUiwwtFsen1AdzAtvuLRsQS8rYgMuxCrdKf9kOssamGLuPwyTY9wyYblNr9+1XM8v6zoDTPPSIeANg==}
    engines: {node: '>=16.0.0'}

  '@aws-crypto/sha256-browser@5.2.0':
    resolution: {integrity: sha512-AXfN/lGotSQwu6HNcEsIASo7kWXZ5HYWvfOmSNKDsEqC4OashTp8alTmaz+F7TC2L083SFv5RdB+qU3Vs1kZqw==}

  '@aws-crypto/sha256-js@5.2.0':
    resolution: {integrity: sha512-FFQQyu7edu4ufvIZ+OadFpHHOt+eSTBaYaki44c+akjg7qZg9oOQeLlk77F6tSYqjDAFClrHJk9tMf0HdVyOvA==}
    engines: {node: '>=16.0.0'}

  '@aws-crypto/supports-web-crypto@5.2.0':
    resolution: {integrity: sha512-iAvUotm021kM33eCdNfwIN//F77/IADDSs58i+MDaOqFrVjZo9bAal0NK7HurRuWLLpF1iLX7gbWrjHjeo+YFg==}

  '@aws-crypto/util@5.2.0':
    resolution: {integrity: sha512-4RkU9EsI6ZpBve5fseQlGNUWKMa1RLPQ1dnjnQoe07ldfIzcsGb5hC5W0Dm7u423KWzawlrpbjXBrXCEv9zazQ==}

  '@aws-sdk/client-bedrock-runtime@3.823.0':
    resolution: {integrity: sha512-mA5ZqVof+M1Oje0tvu/GcD44boQJDUErN5oIDwHH/IV9pvgAB4Dp8zZyQztd/75rzFb5/TUeiWfj8bN+JPnx3A==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/client-sso@3.823.0':
    resolution: {integrity: sha512-dBWdsbyGw8rPfdCsZySNtTOGQK4EZ8lxB/CneSQWRBPHgQ+Ys88NXxImO8xfWO7Itt1eh8O7UDTZ9+smcvw2pw==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/core@3.823.0':
    resolution: {integrity: sha512-1Cf4w8J7wYexz0KU3zpaikHvldGXQEjFldHOhm0SBGRy7qfYNXecfJAamccF7RdgLxKGgkv5Pl9zX/Z/DcW9zg==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/credential-provider-env@3.823.0':
    resolution: {integrity: sha512-AIrLLwumObge+U1klN4j5ToIozI+gE9NosENRyHe0GIIZgTLOG/8jxrMFVYFeNHs7RUtjDTxxewislhFyGxJ/w==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/credential-provider-http@3.823.0':
    resolution: {integrity: sha512-u4DXvB/J/o2bcvP1JP6n3ch7V3/NngmiJFPsM0hKUyRlLuWM37HEDEdjPRs3/uL/soTxrEhWKTA9//YVkvzI0w==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/credential-provider-ini@3.823.0':
    resolution: {integrity: sha512-C0o63qviK5yFvjH9zKWAnCUBkssJoQ1A1XAHe0IAQkurzoNBSmu9oVemqwnKKHA4H6QrmusaEERfL00yohIkJA==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/credential-provider-node@3.823.0':
    resolution: {integrity: sha512-nfSxXVuZ+2GJDpVFlflNfh55Yb4BtDsXLGNssXF5YU6UgSPsi8j2YkaE92Jv2s7dlUK07l0vRpLyPuXMaGeiRQ==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/credential-provider-process@3.823.0':
    resolution: {integrity: sha512-U/A10/7zu2FbMFFVpIw95y0TZf+oYyrhZTBn9eL8zgWcrYRqxrxdqtPj/zMrfIfyIvQUhuJSENN4dx4tfpCMWQ==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/credential-provider-sso@3.823.0':
    resolution: {integrity: sha512-ff8IM80Wqz1V7VVMaMUqO2iR417jggfGWLPl8j2l7uCgwpEyop1ZZl5CFVYEwSupRBtwp+VlW1gTCk7ke56MUw==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/credential-provider-web-identity@3.823.0':
    resolution: {integrity: sha512-lzoZdJMQq9w7i4lXVka30cVBe/dZoUDZST8Xz/soEd73gg7RTKgG+0szL4xFWgdBDgcJDWLfZfJzlbyIVyAyOA==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/eventstream-handler-node@3.821.0':
    resolution: {integrity: sha512-JqmzOCAnd9pUnmbrqXIbyBUxjw/UAfXAu8KAsE/4SveUIvyYRbYSTfCoPq6nnNJQpBtdEFLkjvBnHKBcInDwkg==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/middleware-eventstream@3.821.0':
    resolution: {integrity: sha512-L+qud1uX1hX7MpRy564dFj4/5sDRKVLToiydvgRy6Rc3pwsVhRpm6/2djMVgDsFI3sYd+JoeTFjEypkoV3LE5Q==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/middleware-host-header@3.821.0':
    resolution: {integrity: sha512-xSMR+sopSeWGx5/4pAGhhfMvGBHioVBbqGvDs6pG64xfNwM5vq5s5v6D04e2i+uSTj4qGa71dLUs5I0UzAK3sw==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/middleware-logger@3.821.0':
    resolution: {integrity: sha512-0cvI0ipf2tGx7fXYEEN5fBeZDz2RnHyb9xftSgUsEq7NBxjV0yTZfLJw6Za5rjE6snC80dRN8+bTNR1tuG89zA==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/middleware-recursion-detection@3.821.0':
    resolution: {integrity: sha512-efmaifbhBoqKG3bAoEfDdcM8hn1psF+4qa7ykWuYmfmah59JBeqHLfz5W9m9JoTwoKPkFcVLWZxnyZzAnVBOIg==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/middleware-user-agent@3.823.0':
    resolution: {integrity: sha512-TKRQK09ld1LrIPExC9rIDpqnMsWcv+eq8ABKFHVo8mDLTSuWx/IiQ4eCh9T5zDuEZcLY4nNYCSzXKqw6XKcMCA==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/nested-clients@3.823.0':
    resolution: {integrity: sha512-/BcyOBubrJnd2gxlbbmNJR1w0Z3OVN/UE8Yz20e+ou+Mijjv7EbtVwmWvio1e3ZjphwdA8tVfPYZKwXmrvHKmQ==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/region-config-resolver@3.821.0':
    resolution: {integrity: sha512-t8og+lRCIIy5nlId0bScNpCkif8sc0LhmtaKsbm0ZPm3sCa/WhCbSZibjbZ28FNjVCV+p0D9RYZx0VDDbtWyjw==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/token-providers@3.823.0':
    resolution: {integrity: sha512-vz6onCb/+g4y+owxGGPMEMdN789dTfBOgz/c9pFv0f01840w9Rrt46l+gjQlnXnx+0KG6wNeBIVhFdbCfV3HyQ==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/types@3.821.0':
    resolution: {integrity: sha512-Znroqdai1a90TlxGaJ+FK1lwC0fHpo97Xjsp5UKGR5JODYm7f9+/fF17ebO1KdoBr/Rm0UIFiF5VmI8ts9F1eA==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/util-endpoints@3.821.0':
    resolution: {integrity: sha512-Uknt/zUZnLE76zaAAPEayOeF5/4IZ2puTFXvcSCWHsi9m3tqbb9UozlnlVqvCZLCRWfQryZQoG2W4XSS3qgk5A==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/util-locate-window@3.804.0':
    resolution: {integrity: sha512-zVoRfpmBVPodYlnMjgVjfGoEZagyRF5IPn3Uo6ZvOZp24chnW/FRstH7ESDHDDRga4z3V+ElUQHKpFDXWyBW5A==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/util-user-agent-browser@3.821.0':
    resolution: {integrity: sha512-irWZHyM0Jr1xhC+38OuZ7JB6OXMLPZlj48thElpsO1ZSLRkLZx5+I7VV6k3sp2yZ7BYbKz/G2ojSv4wdm7XTLw==}

  '@aws-sdk/util-user-agent-node@3.823.0':
    resolution: {integrity: sha512-WvNeRz7HV3JLBVGTXW4Qr5QvvWY0vtggH5jW/NqHFH+ZEliVQaUIJ/HNLMpMoCSiu/DlpQAyAjRZXAptJ0oqbw==}
    engines: {node: '>=18.0.0'}
    peerDependencies:
      aws-crt: '>=1.0.0'
    peerDependenciesMeta:
      aws-crt:
        optional: true

  '@aws-sdk/xml-builder@3.821.0':
    resolution: {integrity: sha512-DIIotRnefVL6DiaHtO6/21DhJ4JZnnIwdNbpwiAhdt/AVbttcE4yw925gsjur0OGv5BTYXQXU3YnANBYnZjuQA==}
    engines: {node: '>=18.0.0'}

  '@ckeditor/ckeditor-cloud-services-collaboration@53.0.0':
    resolution: {integrity: sha512-fvPDzpWW6X0cPaz0nTV2e1kKIGCCUZWCYcgaY8Nfk/Lol1uqqhOaYqzL33ONDPf/TeEIrBNXDU+WtBPWLGMNIQ==}
    peerDependencies:
      '@ckeditor/ckeditor5-utils': '>= 37.0'
      ckeditor5: '>= 37.0'

  '@ckeditor/ckeditor5-adapter-ckfinder@46.0.0':
    resolution: {integrity: sha512-Y5OrOyHHgnjA9ferph822U9eJdWqIFbPoRpUTcs/kXWKmQBCvAqPOoGJGAz6O9TCZoGUqjK71eVKV0jSnSo22A==}

  '@ckeditor/ckeditor5-ai@46.0.0':
    resolution: {integrity: sha512-56CPl4LETFZn+uzN7A9cG8Bj7hZnREGUIp4IhmnGRZvLr5wFWtfL8XhQh8huo7hBJ0uhAVY5pDB2owLkfz4ITw==}

  '@ckeditor/ckeditor5-alignment@46.0.0':
    resolution: {integrity: sha512-GNyqLCASALv9hygMfj0lvsl2WAMbOLNcezjjlapYYiZDJSWrxWkYI8Sl+GojjWYyIovwBaGHwJM1IDYKB3mMgw==}

  '@ckeditor/ckeditor5-autoformat@46.0.0':
    resolution: {integrity: sha512-/PU0QlSKHTemYzELG1cQWn7GQ2AYKb3+pSvM3sTgxYkel00KZcmmh0Nl18nyJrlKqn7xKAqEg8XwjsF17I/fJA==}

  '@ckeditor/ckeditor5-autosave@46.0.0':
    resolution: {integrity: sha512-xIaLvCg6ijac41UyPMIfBvS1Gc1u9Y4OcvGln3GCskdFVyjNhHOe4p8yaTavwJ78Dc+59zDIP0rGV8AYWFhyYA==}

  '@ckeditor/ckeditor5-basic-styles@46.0.0':
    resolution: {integrity: sha512-i56SO1n0Xuclhel4Ol362khimK9NQFv4NEcnP058b7JK7JXVDZlyyNHh+J8nTALWokmoLPvj823/2F4yKC8oRQ==}

  '@ckeditor/ckeditor5-block-quote@46.0.0':
    resolution: {integrity: sha512-ASZM3p0wzg2Q4OMsEFhyyi3mN8EUl02LhOxZ41eKa1H2qtAUi+aIFEYNf69raWZ2tqzLfm0z2wTNROzPa3KCJw==}

  '@ckeditor/ckeditor5-bookmark@46.0.0':
    resolution: {integrity: sha512-E52IERdDfNKuagMUkFju6ScFqDIDpXYlLhOmOLv7C/Xh8vKJtyufjjDl/OkhBdMa6gaaHug4a9F7OfCtE+P/Lw==}

  '@ckeditor/ckeditor5-case-change@46.0.0':
    resolution: {integrity: sha512-HAsZ/8wywWDP5kWmBxy8YkIS7kxwK2YLmzro+u7rwMwnxXw+DaoMtCeTZcFehwBC2mzN6DfMUXZ4epl9IfNK/g==}

  '@ckeditor/ckeditor5-ckbox@46.0.0':
    resolution: {integrity: sha512-3RWEx+H7+RKlblI579w8AnlDO3JeRFHmNxh2XolSGohy1bqTttATi5WZVVmGK6quIgFss4VmRpuzZYPpiciuIQ==}

  '@ckeditor/ckeditor5-ckfinder@46.0.0':
    resolution: {integrity: sha512-g7bhFrrlEUi/QbRkGbNRKeeZCrXU7DkLkGsMBypb0m/7hFF0M3XivXNxh9MxrBjIbCyLjnCPXZVL3dLcapHUww==}

  '@ckeditor/ckeditor5-clipboard@46.0.0':
    resolution: {integrity: sha512-FNXyYk8lZKCdpOOql8rTGnTyW6eRo07i4jTSb0sCOOc/x3iAfhyw0ckHI11hi7U3cYE0fOf8WZvp2fn0NfsPVg==}

  '@ckeditor/ckeditor5-cloud-services@46.0.0':
    resolution: {integrity: sha512-ai1OjKmoWmBK/RY5jUXVFAX92szMrK/UcVcu1QHwYBoBNqxoQajsLyzBd+QUg3Z3AdQCpLEJqabgsfizMyrpiA==}

  '@ckeditor/ckeditor5-code-block@46.0.0':
    resolution: {integrity: sha512-9rJqjpW/RY/kKCYEWMchTaTuGyMnUHspteBumtFOnwJS6PVaA1uJU5FOAPSFevZx9KzJBoXbehxtZWmUjImLMQ==}

  '@ckeditor/ckeditor5-collaboration-core@46.0.0':
    resolution: {integrity: sha512-2avV+/PwoPI8qKelprjLoush3PqEME3q1iwh6gTqqN+Ayy7Zc7jV5yEl5HIUZO5L0WzHYkiBZjfmRCxUxL8Hsg==}

  '@ckeditor/ckeditor5-comments@46.0.0':
    resolution: {integrity: sha512-02ji0gIeLuVBopZfmw9hPOWnfDtA9MgDUtCPWI9rqjhg9k0u/ZcYGBFqsL6mUu7h/YaY1n8Z4aKIuArQFwYc3Q==}

  '@ckeditor/ckeditor5-core@46.0.0':
    resolution: {integrity: sha512-Mm5bZVE6i2vcmwaCPvXgNGsVZwgQfb7DZhphWMZD7z4yfCJjqiJ+3ph6ZHtByHLcbUhq1Z8mn8Q+23Y/BVXfhg==}

  '@ckeditor/ckeditor5-document-outline@46.0.0':
    resolution: {integrity: sha512-aLOQuPHXnHKzjQOm2Gg8O0WfzEAUJ0bDuM3nAE7/rqRja2bke2fBqWiy0pGMtVuy+TdYHpn+YGV5o012JNeKtg==}

  '@ckeditor/ckeditor5-easy-image@46.0.0':
    resolution: {integrity: sha512-y9rnIlbQRSe3FRdsoAaL0zpW6jlYPxIpCGT28WdWAF3kLwMp/41oKz6ggjbgzAafobUsbPdjpOzoLEDb8RyMcw==}

  '@ckeditor/ckeditor5-editor-balloon@46.0.0':
    resolution: {integrity: sha512-mDkeX4Lu9NG8gWW1hWPQvR2ZJ7JqKXBfkxRgI7D3PrwrfDyzT2XllfQ31ZjvdUwyUUWyJjIV5AkjsjwWerZHdQ==}

  '@ckeditor/ckeditor5-editor-classic@46.0.0':
    resolution: {integrity: sha512-o0JLR+7KEydjEuHepkaWbi1VGzocxRik7/LiN60jvzeue48YF3ELtD1czRa5eebuAzh3FSL2r3hfbqpagxGlBg==}

  '@ckeditor/ckeditor5-editor-decoupled@46.0.0':
    resolution: {integrity: sha512-HkCfauugtLQxD87A4HcFq2nMxnYq9yngh5JZTPygDSDigRMjNtbdk4PVqMt7p96yw6epJs1HVIZDlFqLljJfLw==}

  '@ckeditor/ckeditor5-editor-inline@46.0.0':
    resolution: {integrity: sha512-m4C6wFkEU16wLdwu2IkTutz+M0u72yqbngMVM95TJ0FNhNQf3T/TVdhjVQxMDlw7Ep+2X3vUDJXMpDQXLNoy1Q==}

  '@ckeditor/ckeditor5-editor-multi-root@46.0.0':
    resolution: {integrity: sha512-WHCibqvsEBVZ0KqlCycqL+7S/5xXt+c4vuLlrKA6hYkF8YQXnxtxIVIWUZp6fEZi0+b75Eke3dOEK4wTY/tDbA==}

  '@ckeditor/ckeditor5-email@46.0.0':
    resolution: {integrity: sha512-Y7ka17XCOMG7ShxLPyukeYaIQazsOjetVbxWlSolJcZS0YW9yQHE9mIUYlwMdgMjZHYe9MHD6WE6V5yMzKrHqA==}

  '@ckeditor/ckeditor5-emoji@46.0.0':
    resolution: {integrity: sha512-XCvkdcujJQeQbYMR6vGzZeOt11+9Q+BbzPh45BAytqzrWN4ngOpJX+BS6ORiZK4f9EsWPgF3kJgJiLVEJhovvw==}

  '@ckeditor/ckeditor5-engine@46.0.0':
    resolution: {integrity: sha512-kuTPDygXaZkILp2A002zDWEZH1gHd6Pw5UfgF+S9f9wEgA0vPLYi/qbeqLkM6D95JQyAfXYPuEkXq6fZdtoZYQ==}

  '@ckeditor/ckeditor5-enter@46.0.0':
    resolution: {integrity: sha512-SAGbScUrB1nazw7dXaGuT/S02bmYvnz/AMRxDkjRAXPqCNE6AUgdCr6dDmXREigQjsR01jvhvx1PAZwUgiC8IQ==}

  '@ckeditor/ckeditor5-essentials@46.0.0':
    resolution: {integrity: sha512-3YuRFelc0f0oerzdrcDvSsMkKDATAB/Z219mjqize0N2RwUA17CCMCbFnGDWZYRekglRQYLTvxd8LCTCmEjQFA==}

  '@ckeditor/ckeditor5-export-inline-styles@46.0.0':
    resolution: {integrity: sha512-0MJXr9h6owrJRn4i8erutlSTsZ2IQRuFib8k987wm5zl7anMSS1p83sDhZezRQu4MX1P+nUOcQDsdVGRS0y1WQ==}

  '@ckeditor/ckeditor5-export-pdf@46.0.0':
    resolution: {integrity: sha512-m7JSpT4nptuqMp58PYkrBPJrEjxVt2FMcYlEHedGgc7po8OcyshHjN6/KcgIPRejyqzKg1wmTwXorTamSJmocw==}

  '@ckeditor/ckeditor5-export-word@46.0.0':
    resolution: {integrity: sha512-TqN6wgNxiiinoIx1eUhJqCsoKMFJBeqfj9Qu32iHgYKN/oHjJkczcQBGluMn0wMW7l+ybjenYKqkMfhPUYgPoQ==}

  '@ckeditor/ckeditor5-find-and-replace@46.0.0':
    resolution: {integrity: sha512-qGw1rOyGVRKQZR7ogNs0XxUb78SkB0xie3LFWZduFAGOaDhC7+DMx91UuO2+lbEaqgGtg1gffNm4Rce//2WdbA==}

  '@ckeditor/ckeditor5-font@46.0.0':
    resolution: {integrity: sha512-Eynyqqre6p+X5ZxlojOfbWuUh8dZdjSnm+8m/7lEOwh8rgzEA5QJ4k91x5BFCYT/yMCesvKTv8H39qIjs/J0Qg==}

  '@ckeditor/ckeditor5-format-painter@46.0.0':
    resolution: {integrity: sha512-224mVroOMf53aX4rdESpYkTvy+hLGQN/sbpOlhFpzuWuS00+rOO9RfJTU6q4tmZh1fr7m+ky61ybONFZz/EZdg==}

  '@ckeditor/ckeditor5-fullscreen@46.0.0':
    resolution: {integrity: sha512-vus25ec9AyGOVcy9ghUTILz8LLctW7dDP2GhUreCX1FEouue8UgvvrfYYLwim1tI7b1deRcg1zIAENHVhjNLiw==}

  '@ckeditor/ckeditor5-heading@46.0.0':
    resolution: {integrity: sha512-fIrmsH4xh3d/QpunSZR3RPR+jybEXBibYwd8EF3ClcpmhhtMRul7BponRVb70a4A4bE+35Pq3Dj+HVV1bn6pIg==}

  '@ckeditor/ckeditor5-highlight@46.0.0':
    resolution: {integrity: sha512-F2jChM/83tcogE3QrxzQJtbCCLhzZY+izpXhiYLO9myfm0guOXITggTbl1LiJ1jdV9iEpq81Lq72/m5mZl5kVw==}

  '@ckeditor/ckeditor5-horizontal-line@46.0.0':
    resolution: {integrity: sha512-5JAYD3nohuqgjPf757XFqi5JWGLkf+5s8SBKFL7b5r6g0WQT0zm7yIxPQGhy0N2eFBFhPW/cdWPa3WhUJYVCmw==}

  '@ckeditor/ckeditor5-html-embed@46.0.0':
    resolution: {integrity: sha512-jHuvg+hIDyjq1zqNoMbR1sbiDBdmd4pZzG5CYcUW2l1DblFzjDq1RUnETHpS+YM0tu4QYYLiMhd9OE4s+bO9eA==}

  '@ckeditor/ckeditor5-html-support@46.0.0':
    resolution: {integrity: sha512-w+CvJ6fBzxXRJ978urZsOKfLXRINzUsfUr4XqUcU0Y9RRXIuyQa/XSRqgAZY2Wrl0t56P+AnolKWJDHx6yiVlQ==}

  '@ckeditor/ckeditor5-icons@46.0.0':
    resolution: {integrity: sha512-WjE4DWF8tnUgFWmvVZswTQnqpWnwSGIZaG1aq11iHFvzK20O7IzJmKzxS6QxOYFeK+I8+sLzbJEDQeZerZb3Kg==}

  '@ckeditor/ckeditor5-image@46.0.0':
    resolution: {integrity: sha512-7NbZB1IrH+2/TJQ07RPqf/o+Qg2PFgtOl0lT8/0nSqCUVbaA5qb2hxoSPB+NGJGeSFRlJEjf0QHMSv6xA2wR8A==}

  '@ckeditor/ckeditor5-import-word@46.0.0':
    resolution: {integrity: sha512-VmWoWJl5Hwj5z5dj6bxsQCMBgV723dLEG8lE2dc+1wU0l7SHNPakQhbGJhFNSfC1ADJp86NCp6yL33O0n8wffw==}

  '@ckeditor/ckeditor5-indent@46.0.0':
    resolution: {integrity: sha512-DGAHxA5NIe4jQ9mnoTpx6EpXKaHFSbqReZBQfZQakW3ypUOjRbpwrQOIEVtRSADaxRM37KcO6oS8duthqqwNDw==}

  '@ckeditor/ckeditor5-language@46.0.0':
    resolution: {integrity: sha512-REricrlKECtJy9E7F6uP+xcYweJU/eCiD5+pHCTosw9/x7XdxwLydwtUosmG9fgvZDeWBEH8SWc9RFCDscPcNQ==}

  '@ckeditor/ckeditor5-line-height@46.0.0':
    resolution: {integrity: sha512-e4REy8taOCwwmDr0QZYD7Wf8LR77kbKmMpNqD22dobVezbyd/v/ryGzQfGDvlL8jOeAjEkzFp2TmgZzgde1CgA==}

  '@ckeditor/ckeditor5-link@46.0.0':
    resolution: {integrity: sha512-R3cCGqgO7FOQJu69yubdoBGaRJ/mK1AB5J4NQXOslNg8us5QIbbL8ZPPbmAY3QtK7w59rJ4eeHJbtAhy28RmWA==}

  '@ckeditor/ckeditor5-list-multi-level@46.0.0':
    resolution: {integrity: sha512-6YcHiNOPyPrGbNxIry+e1xoA9WRUc+1W7SuE5V1GIr87awzyNeMZNJKsqaVFXv/mIJ3kdS39id6gYOUKpc37wg==}

  '@ckeditor/ckeditor5-list@46.0.0':
    resolution: {integrity: sha512-TsEUVFFT/MsJ8JLzE4YAmDDB9kImOBx7+pATN5jQJzKB2pgAoEihTytnWni45nEcXOKj8rOf3CvXTl5rVJfl2Q==}

  '@ckeditor/ckeditor5-markdown-gfm@46.0.0':
    resolution: {integrity: sha512-uDHTS+VrmUY/bZWDQv/9nOOSMnfLOymAmvQ/oTNPlIZQXxprDU1cH221TxOZpvE0u1SHC3Spomt38Ea6yqzGZw==}

  '@ckeditor/ckeditor5-media-embed@46.0.0':
    resolution: {integrity: sha512-ZC8JbaWAxGaC7bANYc7ADOKZtHJFHNsftwKWt0xb2mmqox8BpJlEo6o4LYakXcPBI/JgnXawwukDia2KR8Bz+g==}

  '@ckeditor/ckeditor5-mention@46.0.0':
    resolution: {integrity: sha512-BWwzitc6UUR+vmBf5Hvb1MNmFsYl5k59YT/dKXl0TIeKPJgygdRTFv7EN95wgA1ROpUXRwjCDCfYP5uuaLfjJQ==}

  '@ckeditor/ckeditor5-merge-fields@46.0.0':
    resolution: {integrity: sha512-AWOSHm/HfpiTFE33A/O6hVE0f0q9JIDjpY4KnBQD4Bprb/PfhZ8ns8rCSvC3vZWSPB5O92n+y9L3LjvJ8ZIuhQ==}

  '@ckeditor/ckeditor5-minimap@46.0.0':
    resolution: {integrity: sha512-HiMv+fb9VBmtxVbsPhiDhfn2g2bLP3QpVXT4gmqtYEZoFV/02JvIqeweKDXtx/hrY4qXqklbKNECrJLEAz9Ssw==}

  '@ckeditor/ckeditor5-operations-compressor@46.0.0':
    resolution: {integrity: sha512-z7FYiUIgwm5fWF//nHdZbnuHIChxtU1BuwsPBETG6BM8lhrvExiB35kdu0FFFTpSUcgHXPUOJ0Z4bQIoEhjUMA==}

  '@ckeditor/ckeditor5-page-break@46.0.0':
    resolution: {integrity: sha512-zhgqdS9CLdJ46PTsMogudKtxyN2SW+aCEFjvXff56j51kvfY9shUfk6I0O8+ChmslWy8v7nM4QAQVTDJC/vSEw==}

  '@ckeditor/ckeditor5-pagination@46.0.0':
    resolution: {integrity: sha512-/AJY7D2db6y+hQMZ465EBrFp8rA0n7V2aX7+FQut9KoJqwnUCNFnAhUaNxe8jNapb7s8Skfql6/fQGIW6whBKg==}

  '@ckeditor/ckeditor5-paragraph@46.0.0':
    resolution: {integrity: sha512-3XCJTzFBGv1G6ZS2zQ8QVfX6waBXW08cvFi7F6hhK44OZYllTgW0l9jpR1TnmcC3VokNa4LQrC1fn/J5Hk8fBQ==}

  '@ckeditor/ckeditor5-paste-from-office-enhanced@46.0.0':
    resolution: {integrity: sha512-hVMgfJ4BTcDZJvxjiNLIiWLrRaEi9PsIa5Enw/LxPhPOcZcOHVFj0ZlQIfHAubhuGAw/AxaKmXswBaoU1cWW5A==}

  '@ckeditor/ckeditor5-paste-from-office@46.0.0':
    resolution: {integrity: sha512-8GaHLbcuG26xPiGtp25yoYqB0wy+0IgDZqSW2m6rM3DsMHrFWilufwArp/9wExTBjeKgdbsgv6BP0RFEA1oXcg==}

  '@ckeditor/ckeditor5-real-time-collaboration@46.0.0':
    resolution: {integrity: sha512-RnboRHSI4lVESFhH/pJCKMJAcLGN61gsQ4mXzx4tNXSScPPioF3KkXCivdkuYgJvDtffRuoUoS5tyjWda353ig==}

  '@ckeditor/ckeditor5-remove-format@46.0.0':
    resolution: {integrity: sha512-bOSzTlhTk8TAvSt7C+yNrS531y/D2ghoFhyexhBb2JU+TAeNXkuyuTSxFc9luWIZf2AKvWe4hU3yyVDIlXT9Bg==}

  '@ckeditor/ckeditor5-restricted-editing@46.0.0':
    resolution: {integrity: sha512-euhQ57RfJFJzRP9SseV4sqWsXY9WRQobCaiW1ENxnwEa4FxmnDiLElR6xZ9jimrG5bN3ldbedjJ79mhKp9VMhA==}

  '@ckeditor/ckeditor5-revision-history@46.0.0':
    resolution: {integrity: sha512-msf49YrSdtHTifyKqfkHxU4LomWBKD4cmOTbXfttiE7V1asPRrDFfp+yWymF3HnDZbma0FfEIg2s0/Q+qR844A==}

  '@ckeditor/ckeditor5-select-all@46.0.0':
    resolution: {integrity: sha512-6SvSkYuwZRJuTy/HwQUGh3HpkxkCkEq8Yfew6YWuwulRUgKwMrD4F9R6ZUqDCornTpP+o6p9UrseSFn/3sgxwQ==}

  '@ckeditor/ckeditor5-show-blocks@46.0.0':
    resolution: {integrity: sha512-hhOANTwo6XC1Y59/O8q+XAh+dqokhMov6/HLeuZ0uxVCuryDQxXh+C1lU6rjFG9EKW0zUoQPViLkOAbx/Nm4xQ==}

  '@ckeditor/ckeditor5-slash-command@46.0.0':
    resolution: {integrity: sha512-B4/DcgWDYtcSKlR8FyQzcLGHJWYsuURb9dV4FD0OYeigzC/+l8c0B1AP0+3ZHtpmIclR2/OFMSYWuetBKhKX1g==}

  '@ckeditor/ckeditor5-source-editing-enhanced@46.0.0':
    resolution: {integrity: sha512-viuf65xAX5m0JZUT2XKGB4Wz4FSVIUcbOaRyw3HOLjqmcj58S+8VMfcRrChvtde1fQvzF28+5haekci9MSUWWg==}

  '@ckeditor/ckeditor5-source-editing@46.0.0':
    resolution: {integrity: sha512-TRIgm1vjSerGfuiOg50/+WNXzDYUonjZLsxvFP3c9EOpTs8vqXjdQP/VeyK4PG3DgbablMsZOZhSi9ci0IO3xQ==}

  '@ckeditor/ckeditor5-special-characters@46.0.0':
    resolution: {integrity: sha512-tfjKoavd6rguOE3bzLl65PWJQJsWtAJCZVvW3B6TrbeT3oEqJ1UhBnnA46LgwvXM5tT94b7xCK2AmcjyQ5eG0A==}

  '@ckeditor/ckeditor5-style@46.0.0':
    resolution: {integrity: sha512-Cm8jHegh4UG+1309qbs/hf7aIYTyqp4/zbBciW8Mcdrct8d2eeIigWPt54sw2PWz3v5ZsUquH4Y4JupiEXsOOw==}

  '@ckeditor/ckeditor5-table@46.0.0':
    resolution: {integrity: sha512-N51XBgCWsxrjMJaw9DNuCF9oYhdQgSRhtDsEhY8Q9+VYdtfSxEcjtLpzlUT2/GIwrZKWeFWuJeB3sba/jdfP1w==}

  '@ckeditor/ckeditor5-template@46.0.0':
    resolution: {integrity: sha512-GMvNkqLyIQ7iQJfevYEdkO8QxdW5e5D2Ao1WFo67fwBB40vB0cGLnPKWS6fagordSErmuKn0I0GfkcgCrpOKxA==}

  '@ckeditor/ckeditor5-theme-lark@46.0.0':
    resolution: {integrity: sha512-gaztcvUQ6KAbNTB1efkciEWwesJIp9CjIM91nOBj72eR5/xZKM+aTbLdiciVFSgB1Ma1qCU3DvQ1vafACO93qQ==}

  '@ckeditor/ckeditor5-track-changes@46.0.0':
    resolution: {integrity: sha512-RJthlQjALgskwd9WedD1CRHAK9ebRn9Jo0vIydPaFa8pl5klqGvZVR6uT8jnJ7yRdYXNa/BY+EHqZxUeRdnORg==}

  '@ckeditor/ckeditor5-typing@46.0.0':
    resolution: {integrity: sha512-xeFrXEpzo9T/bLaIwHtYDSSPb5KYA2eiYPVWxivw3VEs6gDfHhRz7EZEZ9OhnjNBQYhiyLgKaX+3di72sqrbhA==}

  '@ckeditor/ckeditor5-ui@46.0.0':
    resolution: {integrity: sha512-VMdUOK42l+b/rHUctXv0WOMbvcqWXM8kglxZ1paAzWBWhhy1cp2Q6VW21w23VSmmwNA110xPKTcKlkC09bniDA==}

  '@ckeditor/ckeditor5-undo@46.0.0':
    resolution: {integrity: sha512-MoLU8c/ip7KVByKZT8fVXSP7ZzxwXu4Rxk/T91rHcxR2BLa93fyS3M9v6oXmww9fI9enGVPw2LD+yESXYJMOiQ==}

  '@ckeditor/ckeditor5-upload@46.0.0':
    resolution: {integrity: sha512-GPRctyLpktYgV00C71R31leUM7rZchEqdvGK+EJfmx8qWkzuKSXLN8HJ9rhjgFf8D49eQm506sH5lek9+HZA0w==}

  '@ckeditor/ckeditor5-uploadcare@46.0.0':
    resolution: {integrity: sha512-w7e9u4VC4qn4rA9Pwfz8XM7H5dEa2bC/dVsJMYihd+K1Mb5uNCrL+W93yENT1TjPKopW2+rPp7IMUSqxFPvzOQ==}

  '@ckeditor/ckeditor5-utils@46.0.0':
    resolution: {integrity: sha512-wql+hSp29EMMhQlj0oXHiOOzD4ZTvRrI7801WtFjfiSldmIJAyIiF4zLFiRoB5+pGbt+AQB76VAdqFjz43AkuA==}

  '@ckeditor/ckeditor5-watchdog@46.0.0':
    resolution: {integrity: sha512-p6lNeUsuWkJ9lJ/59TkCOUrOhUxcB2NGm3sGsG3qHSB/I6W6jjW3r7ZZah/z3Ysy0ImkHLQX0WlFDlw7E6EnSw==}

  '@ckeditor/ckeditor5-widget@46.0.0':
    resolution: {integrity: sha512-NhTtCkkZQNzKHPtUvN5ukOUfwI3rop0Zdo9QxfKFcrSc5B7a9+YLX70lyPzp6loUypy9N9XP4qvvjBUXg64UMw==}

  '@ckeditor/ckeditor5-word-count@46.0.0':
    resolution: {integrity: sha512-wCoQm66jgkFcW8w2acqkUbTzxFt9dkFilU+cMgLOrxBdYarG4T9Xdp2bk6CQyVYf3s+be7N+QGwJGsIyhWT+Gw==}

  '@codemirror/autocomplete@6.18.6':
    resolution: {integrity: sha512-PHHBXFomUs5DF+9tCOM/UoW6XQ4R44lLNNhRaW9PKPTU0D7lIjRg3ElxaJnTwsl/oHiR93WSXDBrekhoUGCPtg==}

  '@codemirror/commands@6.8.1':
    resolution: {integrity: sha512-KlGVYufHMQzxbdQONiLyGQDUW0itrLZwq3CcY7xpv9ZLRHqzkBSoteocBHtMCoY7/Ci4xhzSrToIeLg7FxHuaw==}

  '@codemirror/lang-css@6.3.1':
    resolution: {integrity: sha512-kr5fwBGiGtmz6l0LSJIbno9QrifNMUusivHbnA1H6Dmqy4HZFte3UAICix1VuKo0lMPKQr2rqB+0BkKi/S3Ejg==}

  '@codemirror/lang-html@6.4.9':
    resolution: {integrity: sha512-aQv37pIMSlueybId/2PVSP6NPnmurFDVmZwzc7jszd2KAF8qd4VBbvNYPXWQq90WIARjsdVkPbw29pszmHws3Q==}

  '@codemirror/lang-javascript@6.2.4':
    resolution: {integrity: sha512-0WVmhp1QOqZ4Rt6GlVGwKJN3KW7Xh4H2q8ZZNGZaP6lRdxXJzmjm4FqvmOojVj6khWJHIb9sp7U/72W7xQgqAA==}

  '@codemirror/lang-markdown@6.3.2':
    resolution: {integrity: sha512-c/5MYinGbFxYl4itE9q/rgN/sMTjOr8XL5OWnC+EaRMLfCbVUmmubTJfdgpfcSS2SCaT7b+Q+xi3l6CgoE+BsA==}

  '@codemirror/language@6.11.2':
    resolution: {integrity: sha512-p44TsNArL4IVXDTbapUmEkAlvWs2CFQbcfc0ymDsis1kH2wh0gcY96AS29c/vp2d0y2Tquk1EDSaawpzilUiAw==}

  '@codemirror/lint@6.8.5':
    resolution: {integrity: sha512-s3n3KisH7dx3vsoeGMxsbRAgKe4O1vbrnKBClm99PU0fWxmxsx5rR2PfqQgIt+2MMJBHbiJ5rfIdLYfB9NNvsA==}

  '@codemirror/state@6.5.2':
    resolution: {integrity: sha512-FVqsPqtPWKVVL3dPSxy8wEF/ymIEuVzF1PK3VbUgrxXpJUSHQWWZz4JMToquRxnkw+36LTamCZG2iua2Ptq0fA==}

  '@codemirror/theme-one-dark@6.1.3':
    resolution: {integrity: sha512-NzBdIvEJmx6fjeremiGp3t/okrLPYT0d9orIc7AFun8oZcRk58aejkqhv6spnz4MLAevrKNPMQYXEWMg4s+sKA==}

  '@codemirror/view@6.38.1':
    resolution: {integrity: sha512-RmTOkE7hRU3OVREqFVITWHz6ocgBjv08GoePscAakgVQfciA3SGCEk7mb9IzwW61cKKmlTpHXG6DUE5Ubx+MGQ==}

  '@esbuild/aix-ppc64@0.21.5':
    resolution: {integrity: sha512-1SDgH6ZSPTlggy1yI6+Dbkiz8xzpHJEVAlF/AM1tHPLsf5STom9rwtjE4hKAF20FfXXNTFqEYXyJNWh1GiZedQ==}
    engines: {node: '>=12'}
    cpu: [ppc64]
    os: [aix]

  '@esbuild/android-arm64@0.21.5':
    resolution: {integrity: sha512-c0uX9VAUBQ7dTDCjq+wdyGLowMdtR/GoC2U5IYk/7D1H1JYC0qseD7+11iMP2mRLN9RcCMRcjC4YMclCzGwS/A==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [android]

  '@esbuild/android-arm@0.21.5':
    resolution: {integrity: sha512-vCPvzSjpPHEi1siZdlvAlsPxXl7WbOVUBBAowWug4rJHb68Ox8KualB+1ocNvT5fjv6wpkX6o/iEpbDrf68zcg==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [android]

  '@esbuild/android-x64@0.21.5':
    resolution: {integrity: sha512-D7aPRUUNHRBwHxzxRvp856rjUHRFW1SdQATKXH2hqA0kAZb1hKmi02OpYRacl0TxIGz/ZmXWlbZgjwWYaCakTA==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [android]

  '@esbuild/darwin-arm64@0.21.5':
    resolution: {integrity: sha512-DwqXqZyuk5AiWWf3UfLiRDJ5EDd49zg6O9wclZ7kUMv2WRFr4HKjXp/5t8JZ11QbQfUS6/cRCKGwYhtNAY88kQ==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [darwin]

  '@esbuild/darwin-x64@0.21.5':
    resolution: {integrity: sha512-se/JjF8NlmKVG4kNIuyWMV/22ZaerB+qaSi5MdrXtd6R08kvs2qCN4C09miupktDitvh8jRFflwGFBQcxZRjbw==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [darwin]

  '@esbuild/freebsd-arm64@0.21.5':
    resolution: {integrity: sha512-5JcRxxRDUJLX8JXp/wcBCy3pENnCgBR9bN6JsY4OmhfUtIHe3ZW0mawA7+RDAcMLrMIZaf03NlQiX9DGyB8h4g==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [freebsd]

  '@esbuild/freebsd-x64@0.21.5':
    resolution: {integrity: sha512-J95kNBj1zkbMXtHVH29bBriQygMXqoVQOQYA+ISs0/2l3T9/kj42ow2mpqerRBxDJnmkUDCaQT/dfNXWX/ZZCQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [freebsd]

  '@esbuild/linux-arm64@0.21.5':
    resolution: {integrity: sha512-ibKvmyYzKsBeX8d8I7MH/TMfWDXBF3db4qM6sy+7re0YXya+K1cem3on9XgdT2EQGMu4hQyZhan7TeQ8XkGp4Q==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [linux]

  '@esbuild/linux-arm@0.21.5':
    resolution: {integrity: sha512-bPb5AHZtbeNGjCKVZ9UGqGwo8EUu4cLq68E95A53KlxAPRmUyYv2D6F0uUI65XisGOL1hBP5mTronbgo+0bFcA==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [linux]

  '@esbuild/linux-ia32@0.21.5':
    resolution: {integrity: sha512-YvjXDqLRqPDl2dvRODYmmhz4rPeVKYvppfGYKSNGdyZkA01046pLWyRKKI3ax8fbJoK5QbxblURkwK/MWY18Tg==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [linux]

  '@esbuild/linux-loong64@0.21.5':
    resolution: {integrity: sha512-uHf1BmMG8qEvzdrzAqg2SIG/02+4/DHB6a9Kbya0XDvwDEKCoC8ZRWI5JJvNdUjtciBGFQ5PuBlpEOXQj+JQSg==}
    engines: {node: '>=12'}
    cpu: [loong64]
    os: [linux]

  '@esbuild/linux-mips64el@0.21.5':
    resolution: {integrity: sha512-IajOmO+KJK23bj52dFSNCMsz1QP1DqM6cwLUv3W1QwyxkyIWecfafnI555fvSGqEKwjMXVLokcV5ygHW5b3Jbg==}
    engines: {node: '>=12'}
    cpu: [mips64el]
    os: [linux]

  '@esbuild/linux-ppc64@0.21.5':
    resolution: {integrity: sha512-1hHV/Z4OEfMwpLO8rp7CvlhBDnjsC3CttJXIhBi+5Aj5r+MBvy4egg7wCbe//hSsT+RvDAG7s81tAvpL2XAE4w==}
    engines: {node: '>=12'}
    cpu: [ppc64]
    os: [linux]

  '@esbuild/linux-riscv64@0.21.5':
    resolution: {integrity: sha512-2HdXDMd9GMgTGrPWnJzP2ALSokE/0O5HhTUvWIbD3YdjME8JwvSCnNGBnTThKGEB91OZhzrJ4qIIxk/SBmyDDA==}
    engines: {node: '>=12'}
    cpu: [riscv64]
    os: [linux]

  '@esbuild/linux-s390x@0.21.5':
    resolution: {integrity: sha512-zus5sxzqBJD3eXxwvjN1yQkRepANgxE9lgOW2qLnmr8ikMTphkjgXu1HR01K4FJg8h1kEEDAqDcZQtbrRnB41A==}
    engines: {node: '>=12'}
    cpu: [s390x]
    os: [linux]

  '@esbuild/linux-x64@0.21.5':
    resolution: {integrity: sha512-1rYdTpyv03iycF1+BhzrzQJCdOuAOtaqHTWJZCWvijKD2N5Xu0TtVC8/+1faWqcP9iBCWOmjmhoH94dH82BxPQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [linux]

  '@esbuild/netbsd-x64@0.21.5':
    resolution: {integrity: sha512-Woi2MXzXjMULccIwMnLciyZH4nCIMpWQAs049KEeMvOcNADVxo0UBIQPfSmxB3CWKedngg7sWZdLvLczpe0tLg==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [netbsd]

  '@esbuild/openbsd-x64@0.21.5':
    resolution: {integrity: sha512-HLNNw99xsvx12lFBUwoT8EVCsSvRNDVxNpjZ7bPn947b8gJPzeHWyNVhFsaerc0n3TsbOINvRP2byTZ5LKezow==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [openbsd]

  '@esbuild/sunos-x64@0.21.5':
    resolution: {integrity: sha512-6+gjmFpfy0BHU5Tpptkuh8+uw3mnrvgs+dSPQXQOv3ekbordwnzTVEb4qnIvQcYXq6gzkyTnoZ9dZG+D4garKg==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [sunos]

  '@esbuild/win32-arm64@0.21.5':
    resolution: {integrity: sha512-Z0gOTd75VvXqyq7nsl93zwahcTROgqvuAcYDUr+vOv8uHhNSKROyU961kgtCD1e95IqPKSQKH7tBTslnS3tA8A==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [win32]

  '@esbuild/win32-ia32@0.21.5':
    resolution: {integrity: sha512-SWXFF1CL2RVNMaVs+BBClwtfZSvDgtL//G/smwAc5oVK/UPu2Gu9tIaRgFmYFFKrmg3SyAjSrElf0TiJ1v8fYA==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [win32]

  '@esbuild/win32-x64@0.21.5':
    resolution: {integrity: sha512-tQd/1efJuzPC6rCFwEvLtci/xNFcTZknmXs98FYDfGE4wP9ClFV98nyKrzJKVPMhdDnjzLhdUyMX4PsQAPjwIw==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [win32]

  '@lezer/common@1.2.3':
    resolution: {integrity: sha512-w7ojc8ejBqr2REPsWxJjrMFsA/ysDCFICn8zEOR9mrqzOu2amhITYuLD8ag6XZf0CFXDrhKqw7+tW8cX66NaDA==}

  '@lezer/css@1.3.0':
    resolution: {integrity: sha512-pBL7hup88KbI7hXnZV3PQsn43DHy6TWyzuyk2AO9UyoXcDltvIdqWKE1dLL/45JVZ+YZkHe1WVHqO6wugZZWcw==}

  '@lezer/highlight@1.2.1':
    resolution: {integrity: sha512-Z5duk4RN/3zuVO7Jq0pGLJ3qynpxUVsh7IbUbGj88+uV2ApSAn6kWg2au3iJb+0Zi7kKtqffIESgNcRXWZWmSA==}

  '@lezer/html@1.3.10':
    resolution: {integrity: sha512-dqpT8nISx/p9Do3AchvYGV3qYc4/rKr3IBZxlHmpIKam56P47RSHkSF5f13Vu9hebS1jM0HmtJIwLbWz1VIY6w==}

  '@lezer/javascript@1.5.1':
    resolution: {integrity: sha512-ATOImjeVJuvgm3JQ/bpo2Tmv55HSScE2MTPnKRMRIPx2cLhHGyX2VnqpHhtIV1tVzIjZDbcWQm+NCTF40ggZVw==}

  '@lezer/lr@1.4.2':
    resolution: {integrity: sha512-pu0K1jCIdnQ12aWNaAVU5bzi7Bd1w54J3ECgANPmYLtQKP0HBj2cE/5coBD66MT10xbtIuUr7tg0Shbsvk0mDA==}

  '@lezer/markdown@1.4.3':
    resolution: {integrity: sha512-kfw+2uMrQ/wy/+ONfrH83OkdFNM0ye5Xq96cLlaCy7h5UT9FO54DU4oRoIc0CSBh5NWmWuiIJA7NGLMJbQ+Oxg==}

  '@marijn/find-cluster-break@1.0.2':
    resolution: {integrity: sha512-l0h88YhZFyKdXIFNfSWpyjStDjGHwZ/U7iobcK1cQQD8sejsONdQtTVU+1wVN1PBw40PiiHB1vA5S7VTfQiP9g==}

  '@protobufjs/aspromise@1.1.2':
    resolution: {integrity: sha512-j+gKExEuLmKwvz3OgROXtrJ2UG2x8Ch2YZUxahh+s1F2HZ+wAceUNLkvy6zKCPVRkU++ZWQrdxsUeQXmcg4uoQ==}

  '@protobufjs/base64@1.1.2':
    resolution: {integrity: sha512-AZkcAA5vnN/v4PDqKyMR5lx7hZttPDgClv83E//FMNhR2TMcLUhfRUBHCmSl0oi9zMgDDqRUJkSxO3wm85+XLg==}

  '@protobufjs/codegen@2.0.4':
    resolution: {integrity: sha512-YyFaikqM5sH0ziFZCN3xDC7zeGaB/d0IUb9CATugHWbd1FRFwWwt4ld4OYMPWu5a3Xe01mGAULCdqhMlPl29Jg==}

  '@protobufjs/eventemitter@1.1.0':
    resolution: {integrity: sha512-j9ednRT81vYJ9OfVuXG6ERSTdEL1xVsNgqpkxMsbIabzSo3goCjDIveeGv5d03om39ML71RdmrGNjG5SReBP/Q==}

  '@protobufjs/fetch@1.1.0':
    resolution: {integrity: sha512-lljVXpqXebpsijW71PZaCYeIcE5on1w5DlQy5WH6GLbFryLUrBD4932W/E2BSpfRJWseIL4v/KPgBFxDOIdKpQ==}

  '@protobufjs/float@1.0.2':
    resolution: {integrity: sha512-Ddb+kVXlXst9d+R9PfTIxh1EdNkgoRe5tOX6t01f1lYWOvJnSPDBlG241QLzcyPdoNTsblLUdujGSE4RzrTZGQ==}

  '@protobufjs/inquire@1.1.0':
    resolution: {integrity: sha512-kdSefcPdruJiFMVSbn801t4vFK7KB/5gd2fYvrxhuJYg8ILrmn9SKSX2tZdV6V+ksulWqS7aXjBcRXl3wHoD9Q==}

  '@protobufjs/path@1.1.2':
    resolution: {integrity: sha512-6JOcJ5Tm08dOHAbdR3GrvP+yUUfkjG5ePsHYczMFLq3ZmMkAD98cDgcT2iA1lJ9NVwFd4tH/iSSoe44YWkltEA==}

  '@protobufjs/pool@1.1.0':
    resolution: {integrity: sha512-0kELaGSIDBKvcgS4zkjz1PeddatrjYcmMWOlAuAPwAeccUrPHdUqo/J6LiymHHEiJT5NrF1UVwxY14f+fy4WQw==}

  '@protobufjs/utf8@1.1.0':
    resolution: {integrity: sha512-Vvn3zZrhQZkkBE8LSuW3em98c0FwgO4nxzv6OdSxPKJIEKY2bGbHn+mhGIPerzI4twdxaP8/0+06HBpwf345Lw==}

  '@rollup/rollup-android-arm-eabi@4.46.2':
    resolution: {integrity: sha512-Zj3Hl6sN34xJtMv7Anwb5Gu01yujyE/cLBDB2gnHTAHaWS1Z38L7kuSG+oAh0giZMqG060f/YBStXtMH6FvPMA==}
    cpu: [arm]
    os: [android]

  '@rollup/rollup-android-arm64@4.46.2':
    resolution: {integrity: sha512-nTeCWY83kN64oQ5MGz3CgtPx8NSOhC5lWtsjTs+8JAJNLcP3QbLCtDDgUKQc/Ro/frpMq4SHUaHN6AMltcEoLQ==}
    cpu: [arm64]
    os: [android]

  '@rollup/rollup-darwin-arm64@4.46.2':
    resolution: {integrity: sha512-HV7bW2Fb/F5KPdM/9bApunQh68YVDU8sO8BvcW9OngQVN3HHHkw99wFupuUJfGR9pYLLAjcAOA6iO+evsbBaPQ==}
    cpu: [arm64]
    os: [darwin]

  '@rollup/rollup-darwin-x64@4.46.2':
    resolution: {integrity: sha512-SSj8TlYV5nJixSsm/y3QXfhspSiLYP11zpfwp6G/YDXctf3Xkdnk4woJIF5VQe0of2OjzTt8EsxnJDCdHd2xMA==}
    cpu: [x64]
    os: [darwin]

  '@rollup/rollup-freebsd-arm64@4.46.2':
    resolution: {integrity: sha512-ZyrsG4TIT9xnOlLsSSi9w/X29tCbK1yegE49RYm3tu3wF1L/B6LVMqnEWyDB26d9Ecx9zrmXCiPmIabVuLmNSg==}
    cpu: [arm64]
    os: [freebsd]

  '@rollup/rollup-freebsd-x64@4.46.2':
    resolution: {integrity: sha512-pCgHFoOECwVCJ5GFq8+gR8SBKnMO+xe5UEqbemxBpCKYQddRQMgomv1104RnLSg7nNvgKy05sLsY51+OVRyiVw==}
    cpu: [x64]
    os: [freebsd]

  '@rollup/rollup-linux-arm-gnueabihf@4.46.2':
    resolution: {integrity: sha512-EtP8aquZ0xQg0ETFcxUbU71MZlHaw9MChwrQzatiE8U/bvi5uv/oChExXC4mWhjiqK7azGJBqU0tt5H123SzVA==}
    cpu: [arm]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-arm-musleabihf@4.46.2':
    resolution: {integrity: sha512-qO7F7U3u1nfxYRPM8HqFtLd+raev2K137dsV08q/LRKRLEc7RsiDWihUnrINdsWQxPR9jqZ8DIIZ1zJJAm5PjQ==}
    cpu: [arm]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-linux-arm64-gnu@4.46.2':
    resolution: {integrity: sha512-3dRaqLfcOXYsfvw5xMrxAk9Lb1f395gkoBYzSFcc/scgRFptRXL9DOaDpMiehf9CO8ZDRJW2z45b6fpU5nwjng==}
    cpu: [arm64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-arm64-musl@4.46.2':
    resolution: {integrity: sha512-fhHFTutA7SM+IrR6lIfiHskxmpmPTJUXpWIsBXpeEwNgZzZZSg/q4i6FU4J8qOGyJ0TR+wXBwx/L7Ho9z0+uDg==}
    cpu: [arm64]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-linux-loongarch64-gnu@4.46.2':
    resolution: {integrity: sha512-i7wfGFXu8x4+FRqPymzjD+Hyav8l95UIZ773j7J7zRYc3Xsxy2wIn4x+llpunexXe6laaO72iEjeeGyUFmjKeA==}
    cpu: [loong64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-ppc64-gnu@4.46.2':
    resolution: {integrity: sha512-B/l0dFcHVUnqcGZWKcWBSV2PF01YUt0Rvlurci5P+neqY/yMKchGU8ullZvIv5e8Y1C6wOn+U03mrDylP5q9Yw==}
    cpu: [ppc64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-riscv64-gnu@4.46.2':
    resolution: {integrity: sha512-32k4ENb5ygtkMwPMucAb8MtV8olkPT03oiTxJbgkJa7lJ7dZMr0GCFJlyvy+K8iq7F/iuOr41ZdUHaOiqyR3iQ==}
    cpu: [riscv64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-riscv64-musl@4.46.2':
    resolution: {integrity: sha512-t5B2loThlFEauloaQkZg9gxV05BYeITLvLkWOkRXogP4qHXLkWSbSHKM9S6H1schf/0YGP/qNKtiISlxvfmmZw==}
    cpu: [riscv64]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-linux-s390x-gnu@4.46.2':
    resolution: {integrity: sha512-YKjekwTEKgbB7n17gmODSmJVUIvj8CX7q5442/CK80L8nqOUbMtf8b01QkG3jOqyr1rotrAnW6B/qiHwfcuWQA==}
    cpu: [s390x]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-x64-gnu@4.46.2':
    resolution: {integrity: sha512-Jj5a9RUoe5ra+MEyERkDKLwTXVu6s3aACP51nkfnK9wJTraCC8IMe3snOfALkrjTYd2G1ViE1hICj0fZ7ALBPA==}
    cpu: [x64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-x64-musl@4.46.2':
    resolution: {integrity: sha512-7kX69DIrBeD7yNp4A5b81izs8BqoZkCIaxQaOpumcJ1S/kmqNFjPhDu1LHeVXv0SexfHQv5cqHsxLOjETuqDuA==}
    cpu: [x64]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-win32-arm64-msvc@4.46.2':
    resolution: {integrity: sha512-wiJWMIpeaak/jsbaq2HMh/rzZxHVW1rU6coyeNNpMwk5isiPjSTx0a4YLSlYDwBH/WBvLz+EtsNqQScZTLJy3g==}
    cpu: [arm64]
    os: [win32]

  '@rollup/rollup-win32-ia32-msvc@4.46.2':
    resolution: {integrity: sha512-gBgaUDESVzMgWZhcyjfs9QFK16D8K6QZpwAaVNJxYDLHWayOta4ZMjGm/vsAEy3hvlS2GosVFlBlP9/Wb85DqQ==}
    cpu: [ia32]
    os: [win32]

  '@rollup/rollup-win32-x64-msvc@4.46.2':
    resolution: {integrity: sha512-CvUo2ixeIQGtF6WvuB87XWqPQkoFAFqW+HUo/WzHwuHDvIwZCtjdWXoYCcr06iKGydiqTclC4jU/TNObC/xKZg==}
    cpu: [x64]
    os: [win32]

  '@smithy/abort-controller@4.0.4':
    resolution: {integrity: sha512-gJnEjZMvigPDQWHrW3oPrFhQtkrgqBkyjj3pCIdF3A5M6vsZODG93KNlfJprv6bp4245bdT32fsHK4kkH3KYDA==}
    engines: {node: '>=18.0.0'}

  '@smithy/config-resolver@4.1.4':
    resolution: {integrity: sha512-prmU+rDddxHOH0oNcwemL+SwnzcG65sBF2yXRO7aeXIn/xTlq2pX7JLVbkBnVLowHLg4/OL4+jBmv9hVrVGS+w==}
    engines: {node: '>=18.0.0'}

  '@smithy/core@3.7.2':
    resolution: {integrity: sha512-JoLw59sT5Bm8SAjFCYZyuCGxK8y3vovmoVbZWLDPTH5XpPEIwpFd9m90jjVMwoypDuB/SdVgje5Y4T7w50lJaw==}
    engines: {node: '>=18.0.0'}

  '@smithy/credential-provider-imds@4.0.6':
    resolution: {integrity: sha512-hKMWcANhUiNbCJouYkZ9V3+/Qf9pteR1dnwgdyzR09R4ODEYx8BbUysHwRSyex4rZ9zapddZhLFTnT4ZijR4pw==}
    engines: {node: '>=18.0.0'}

  '@smithy/eventstream-codec@4.0.4':
    resolution: {integrity: sha512-7XoWfZqWb/QoR/rAU4VSi0mWnO2vu9/ltS6JZ5ZSZv0eovLVfDfu0/AX4ub33RsJTOth3TiFWSHS5YdztvFnig==}
    engines: {node: '>=18.0.0'}

  '@smithy/eventstream-serde-browser@4.0.4':
    resolution: {integrity: sha512-3fb/9SYaYqbpy/z/H3yIi0bYKyAa89y6xPmIqwr2vQiUT2St+avRt8UKwsWt9fEdEasc5d/V+QjrviRaX1JRFA==}
    engines: {node: '>=18.0.0'}

  '@smithy/eventstream-serde-config-resolver@4.1.2':
    resolution: {integrity: sha512-JGtambizrWP50xHgbzZI04IWU7LdI0nh/wGbqH3sJesYToMi2j/DcoElqyOcqEIG/D4tNyxgRuaqBXWE3zOFhQ==}
    engines: {node: '>=18.0.0'}

  '@smithy/eventstream-serde-node@4.0.4':
    resolution: {integrity: sha512-RD6UwNZ5zISpOWPuhVgRz60GkSIp0dy1fuZmj4RYmqLVRtejFqQ16WmfYDdoSoAjlp1LX+FnZo+/hkdmyyGZ1w==}
    engines: {node: '>=18.0.0'}

  '@smithy/eventstream-serde-universal@4.0.4':
    resolution: {integrity: sha512-UeJpOmLGhq1SLox79QWw/0n2PFX+oPRE1ZyRMxPIaFEfCqWaqpB7BU9C8kpPOGEhLF7AwEqfFbtwNxGy4ReENA==}
    engines: {node: '>=18.0.0'}

  '@smithy/fetch-http-handler@5.1.0':
    resolution: {integrity: sha512-mADw7MS0bYe2OGKkHYMaqarOXuDwRbO6ArD91XhHcl2ynjGCFF+hvqf0LyQcYxkA1zaWjefSkU7Ne9mqgApSgQ==}
    engines: {node: '>=18.0.0'}

  '@smithy/hash-node@4.0.4':
    resolution: {integrity: sha512-qnbTPUhCVnCgBp4z4BUJUhOEkVwxiEi1cyFM+Zj6o+aY8OFGxUQleKWq8ltgp3dujuhXojIvJWdoqpm6dVO3lQ==}
    engines: {node: '>=18.0.0'}

  '@smithy/invalid-dependency@4.0.4':
    resolution: {integrity: sha512-bNYMi7WKTJHu0gn26wg8OscncTt1t2b8KcsZxvOv56XA6cyXtOAAAaNP7+m45xfppXfOatXF3Sb1MNsLUgVLTw==}
    engines: {node: '>=18.0.0'}

  '@smithy/is-array-buffer@2.2.0':
    resolution: {integrity: sha512-GGP3O9QFD24uGeAXYUjwSTXARoqpZykHadOmA8G5vfJPK0/DC67qa//0qvqrJzL1xc8WQWX7/yc7fwudjPHPhA==}
    engines: {node: '>=14.0.0'}

  '@smithy/is-array-buffer@4.0.0':
    resolution: {integrity: sha512-saYhF8ZZNoJDTvJBEWgeBccCg+yvp1CX+ed12yORU3NilJScfc6gfch2oVb4QgxZrGUx3/ZJlb+c/dJbyupxlw==}
    engines: {node: '>=18.0.0'}

  '@smithy/middleware-content-length@4.0.4':
    resolution: {integrity: sha512-F7gDyfI2BB1Kc+4M6rpuOLne5LOcEknH1n6UQB69qv+HucXBR1rkzXBnQTB2q46sFy1PM/zuSJOB532yc8bg3w==}
    engines: {node: '>=18.0.0'}

  '@smithy/middleware-endpoint@4.1.17':
    resolution: {integrity: sha512-S3hSGLKmHG1m35p/MObQCBCdRsrpbPU8B129BVzRqRfDvQqPMQ14iO4LyRw+7LNizYc605COYAcjqgawqi+6jA==}
    engines: {node: '>=18.0.0'}

  '@smithy/middleware-retry@4.1.18':
    resolution: {integrity: sha512-bYLZ4DkoxSsPxpdmeapvAKy7rM5+25gR7PGxq2iMiecmbrRGBHj9s75N74Ylg+aBiw9i5jIowC/cLU2NR0qH8w==}
    engines: {node: '>=18.0.0'}

  '@smithy/middleware-serde@4.0.8':
    resolution: {integrity: sha512-iSSl7HJoJaGyMIoNn2B7czghOVwJ9nD7TMvLhMWeSB5vt0TnEYyRRqPJu/TqW76WScaNvYYB8nRoiBHR9S1Ddw==}
    engines: {node: '>=18.0.0'}

  '@smithy/middleware-stack@4.0.4':
    resolution: {integrity: sha512-kagK5ggDrBUCCzI93ft6DjteNSfY8Ulr83UtySog/h09lTIOAJ/xUSObutanlPT0nhoHAkpmW9V5K8oPyLh+QA==}
    engines: {node: '>=18.0.0'}

  '@smithy/node-config-provider@4.1.3':
    resolution: {integrity: sha512-HGHQr2s59qaU1lrVH6MbLlmOBxadtzTsoO4c+bF5asdgVik3I8o7JIOzoeqWc5MjVa+vD36/LWE0iXKpNqooRw==}
    engines: {node: '>=18.0.0'}

  '@smithy/node-http-handler@4.1.0':
    resolution: {integrity: sha512-vqfSiHz2v8b3TTTrdXi03vNz1KLYYS3bhHCDv36FYDqxT7jvTll1mMnCrkD+gOvgwybuunh/2VmvOMqwBegxEg==}
    engines: {node: '>=18.0.0'}

  '@smithy/property-provider@4.0.4':
    resolution: {integrity: sha512-qHJ2sSgu4FqF4U/5UUp4DhXNmdTrgmoAai6oQiM+c5RZ/sbDwJ12qxB1M6FnP+Tn/ggkPZf9ccn4jqKSINaquw==}
    engines: {node: '>=18.0.0'}

  '@smithy/protocol-http@5.1.2':
    resolution: {integrity: sha512-rOG5cNLBXovxIrICSBm95dLqzfvxjEmuZx4KK3hWwPFHGdW3lxY0fZNXfv2zebfRO7sJZ5pKJYHScsqopeIWtQ==}
    engines: {node: '>=18.0.0'}

  '@smithy/querystring-builder@4.0.4':
    resolution: {integrity: sha512-SwREZcDnEYoh9tLNgMbpop+UTGq44Hl9tdj3rf+yeLcfH7+J8OXEBaMc2kDxtyRHu8BhSg9ADEx0gFHvpJgU8w==}
    engines: {node: '>=18.0.0'}

  '@smithy/querystring-parser@4.0.4':
    resolution: {integrity: sha512-6yZf53i/qB8gRHH/l2ZwUG5xgkPgQF15/KxH0DdXMDHjesA9MeZje/853ifkSY0x4m5S+dfDZ+c4x439PF0M2w==}
    engines: {node: '>=18.0.0'}

  '@smithy/service-error-classification@4.0.6':
    resolution: {integrity: sha512-RRoTDL//7xi4tn5FrN2NzH17jbgmnKidUqd4KvquT0954/i6CXXkh1884jBiunq24g9cGtPBEXlU40W6EpNOOg==}
    engines: {node: '>=18.0.0'}

  '@smithy/shared-ini-file-loader@4.0.4':
    resolution: {integrity: sha512-63X0260LoFBjrHifPDs+nM9tV0VMkOTl4JRMYNuKh/f5PauSjowTfvF3LogfkWdcPoxsA9UjqEOgjeYIbhb7Nw==}
    engines: {node: '>=18.0.0'}

  '@smithy/signature-v4@5.1.2':
    resolution: {integrity: sha512-d3+U/VpX7a60seHziWnVZOHuEgJlclufjkS6zhXvxcJgkJq4UWdH5eOBLzHRMx6gXjsdT9h6lfpmLzbrdupHgQ==}
    engines: {node: '>=18.0.0'}

  '@smithy/smithy-client@4.4.9':
    resolution: {integrity: sha512-mbMg8mIUAWwMmb74LoYiArP04zWElPzDoA1jVOp3or0cjlDMgoS6WTC3QXK0Vxoc9I4zdrX0tq6qsOmaIoTWEQ==}
    engines: {node: '>=18.0.0'}

  '@smithy/types@4.3.1':
    resolution: {integrity: sha512-UqKOQBL2x6+HWl3P+3QqFD4ncKq0I8Nuz9QItGv5WuKuMHuuwlhvqcZCoXGfc+P1QmfJE7VieykoYYmrOoFJxA==}
    engines: {node: '>=18.0.0'}

  '@smithy/url-parser@4.0.4':
    resolution: {integrity: sha512-eMkc144MuN7B0TDA4U2fKs+BqczVbk3W+qIvcoCY6D1JY3hnAdCuhCZODC+GAeaxj0p6Jroz4+XMUn3PCxQQeQ==}
    engines: {node: '>=18.0.0'}

  '@smithy/util-base64@4.0.0':
    resolution: {integrity: sha512-CvHfCmO2mchox9kjrtzoHkWHxjHZzaFojLc8quxXY7WAAMAg43nuxwv95tATVgQFNDwd4M9S1qFzj40Ul41Kmg==}
    engines: {node: '>=18.0.0'}

  '@smithy/util-body-length-browser@4.0.0':
    resolution: {integrity: sha512-sNi3DL0/k64/LO3A256M+m3CDdG6V7WKWHdAiBBMUN8S3hK3aMPhwnPik2A/a2ONN+9doY9UxaLfgqsIRg69QA==}
    engines: {node: '>=18.0.0'}

  '@smithy/util-body-length-node@4.0.0':
    resolution: {integrity: sha512-q0iDP3VsZzqJyje8xJWEJCNIu3lktUGVoSy1KB0UWym2CL1siV3artm+u1DFYTLejpsrdGyCSWBdGNjJzfDPjg==}
    engines: {node: '>=18.0.0'}

  '@smithy/util-buffer-from@2.2.0':
    resolution: {integrity: sha512-IJdWBbTcMQ6DA0gdNhh/BwrLkDR+ADW5Kr1aZmd4k3DIF6ezMV4R2NIAmT08wQJ3yUK82thHWmC/TnK/wpMMIA==}
    engines: {node: '>=14.0.0'}

  '@smithy/util-buffer-from@4.0.0':
    resolution: {integrity: sha512-9TOQ7781sZvddgO8nxueKi3+yGvkY35kotA0Y6BWRajAv8jjmigQ1sBwz0UX47pQMYXJPahSKEKYFgt+rXdcug==}
    engines: {node: '>=18.0.0'}

  '@smithy/util-config-provider@4.0.0':
    resolution: {integrity: sha512-L1RBVzLyfE8OXH+1hsJ8p+acNUSirQnWQ6/EgpchV88G6zGBTDPdXiiExei6Z1wR2RxYvxY/XLw6AMNCCt8H3w==}
    engines: {node: '>=18.0.0'}

  '@smithy/util-defaults-mode-browser@4.0.25':
    resolution: {integrity: sha512-pxEWsxIsOPLfKNXvpgFHBGFC3pKYKUFhrud1kyooO9CJai6aaKDHfT10Mi5iiipPXN/JhKAu3qX9o75+X85OdQ==}
    engines: {node: '>=18.0.0'}

  '@smithy/util-defaults-mode-node@4.0.25':
    resolution: {integrity: sha512-+w4n4hKFayeCyELZLfsSQG5mCC3TwSkmRHv4+el5CzFU8ToQpYGhpV7mrRzqlwKkntlPilT1HJy1TVeEvEjWOQ==}
    engines: {node: '>=18.0.0'}

  '@smithy/util-endpoints@3.0.6':
    resolution: {integrity: sha512-YARl3tFL3WgPuLzljRUnrS2ngLiUtkwhQtj8PAL13XZSyUiNLQxwG3fBBq3QXFqGFUXepIN73pINp3y8c2nBmA==}
    engines: {node: '>=18.0.0'}

  '@smithy/util-hex-encoding@4.0.0':
    resolution: {integrity: sha512-Yk5mLhHtfIgW2W2WQZWSg5kuMZCVbvhFmC7rV4IO2QqnZdbEFPmQnCcGMAX2z/8Qj3B9hYYNjZOhWym+RwhePw==}
    engines: {node: '>=18.0.0'}

  '@smithy/util-middleware@4.0.4':
    resolution: {integrity: sha512-9MLKmkBmf4PRb0ONJikCbCwORACcil6gUWojwARCClT7RmLzF04hUR4WdRprIXal7XVyrddadYNfp2eF3nrvtQ==}
    engines: {node: '>=18.0.0'}

  '@smithy/util-retry@4.0.6':
    resolution: {integrity: sha512-+YekoF2CaSMv6zKrA6iI/N9yva3Gzn4L6n35Luydweu5MMPYpiGZlWqehPHDHyNbnyaYlz/WJyYAZnC+loBDZg==}
    engines: {node: '>=18.0.0'}

  '@smithy/util-stream@4.2.3':
    resolution: {integrity: sha512-cQn412DWHHFNKrQfbHY8vSFI3nTROY1aIKji9N0tpp8gUABRilr7wdf8fqBbSlXresobM+tQFNk6I+0LXK/YZg==}
    engines: {node: '>=18.0.0'}

  '@smithy/util-uri-escape@4.0.0':
    resolution: {integrity: sha512-77yfbCbQMtgtTylO9itEAdpPXSog3ZxMe09AEhm0dU0NLTalV70ghDZFR+Nfi1C60jnJoh/Re4090/DuZh2Omg==}
    engines: {node: '>=18.0.0'}

  '@smithy/util-utf8@2.3.0':
    resolution: {integrity: sha512-R8Rdn8Hy72KKcebgLiv8jQcQkXoLMOGGv5uI1/k0l+snqkOzQ1R0ChUBCxWMlBsFMekWjq0wRudIweFs7sKT5A==}
    engines: {node: '>=14.0.0'}

  '@smithy/util-utf8@4.0.0':
    resolution: {integrity: sha512-b+zebfKCfRdgNJDknHCob3O7FpeYQN6ZG6YLExMcasDHsCXlsXCEuiPZeLnJLpwa5dvPetGlnGCiMHuLwGvFow==}
    engines: {node: '>=18.0.0'}

  '@socket.io/component-emitter@3.1.2':
    resolution: {integrity: sha512-9BCxFwvbGg/RsZK9tjXd8s4UcwR0MWeFQ1XEKIQVVvAGJyINdrqKMcTRyLoK8Rse1GjzLV9cwjWV1olXRWEXVA==}

  '@symbiotejs/symbiote@1.11.7':
    resolution: {integrity: sha512-fUOJwzuldeApJ533YeTdrfnpp4nsA+ss1eiNBodX7RHf4LnhPB2Z9HP4fF3m2YhKYnxK0whjXaKA+wrxTRP5qA==}

  '@types/color-convert@2.0.4':
    resolution: {integrity: sha512-Ub1MmDdyZ7mX//g25uBAoH/mWGd9swVbt8BseymnaE18SU4po/PjmCrHxqIIRjBo3hV/vh1KGr0eMxUhp+t+dQ==}

  '@types/color-name@1.1.5':
    resolution: {integrity: sha512-j2K5UJqGTxeesj6oQuGpMgifpT5k9HprgQd8D1Y0lOFqKHl3PJu5GMeS4Y5EgjS55AE6OQxf8mPED9uaGbf4Cg==}

  '@types/debug@4.1.12':
    resolution: {integrity: sha512-vIChWdVG3LG1SMxEvI/AK+FWJthlrqlTu7fbrlywTkkaONwk/UAGaULXRlf8vkzFBLVm0zkMdCquhL5aOjhXPQ==}

  '@types/estree@1.0.8':
    resolution: {integrity: sha512-dWHzHa2WqEXI/O1E9OjrocMTKJl2mSrEolh1Iomrv6U+JuNwaHXsXx9bLu5gG7BUWFIN0skIQJQ/L1rIex4X6w==}

  '@types/hast@3.0.4':
    resolution: {integrity: sha512-WPs+bbQw5aCj+x6laNGWLH3wviHtoCv/P3+otBhbOhJgG8qtpdAMlTCxLtsTWA7LH1Oh/bFCHsBn0TPS5m30EQ==}

  '@types/luxon@3.6.2':
    resolution: {integrity: sha512-R/BdP7OxEMc44l2Ex5lSXHoIXTB2JLNa3y2QISIbr58U/YcsffyQrYW//hZSdrfxrjRZj3GcUoxMPGdO8gSYuw==}

  '@types/mdast@4.0.4':
    resolution: {integrity: sha512-kGaNbPh1k7AFzgpud/gMdvIm5xuECykRR+JnWKQno9TAXVa6WIVCGTPvYGekIDL4uwCZQSYbUxNBSb1aUo79oA==}

  '@types/ms@2.1.0':
    resolution: {integrity: sha512-GsCCIZDE/p3i96vtEqx+7dBUGXrc7zeSK3wwPHIaRThS+9OhWIXRqzs4d6k1SVU8g91DrNRWxWUGhp5KXQb2VA==}

  '@types/node@24.1.0':
    resolution: {integrity: sha512-ut5FthK5moxFKH2T1CUOC6ctR67rQRvvHdFLCD2Ql6KXmMuCrjsSsRI9UsLCm9M18BMwClv4pn327UvB7eeO1w==}

  '@types/unist@3.0.3':
    resolution: {integrity: sha512-ko/gIFJRv177XgZsZcBwnqJN5x/Gien8qNOn0D5bQU/zAzVf9Zt3BlcUiLqhV9y4ARk0GbT3tnUiPNgnTXzc/Q==}

  '@types/uuid@9.0.8':
    resolution: {integrity: sha512-jg+97EGIcY9AGHJJRaaPVgetKDsrTgbRjQ5Msgjh/DQKEFl0DtyRr/VCOyD1T2R1MNeWPK/u7JoGhlDZnKBAfA==}

  '@ungap/structured-clone@1.3.0':
    resolution: {integrity: sha512-WmoN8qaIAo7WTYWbAZuG8PYEhn5fkz7dZrqTBZ7dtt//lL2Gwms1IcnQ5yHqjDfX8Ft5j4YzDM23f87zBfDe9g==}

  '@uploadcare/file-uploader@1.16.3-alpha.0':
    resolution: {integrity: sha512-QTOJ62NmJ8+G1LX72Y7+Z116YRiRs7EfSEdXNSZPQFhlpooDe1Djdw+UmJv7Kz91aXHhz2DS1gandxz7iqWK0Q==}

  '@uploadcare/image-shrink@6.17.0':
    resolution: {integrity: sha512-9ZIQR2Uad7BeSH0hqo7W/VenHCwnEqRjFpN6SGynTzG6BxoH/HK8NkuMKdJ8URFisW7dZSXpVMZrCL5kf7FfKA==}

  '@uploadcare/upload-client@6.14.3':
    resolution: {integrity: sha512-uZDXb2IuFchpNQdHDxDowKgGPd+9UOy0PIykWEPedMbbBYxh7/UUQ+G53E4KhKe7cV7BV4zoFxUrNx+Ij0lyPw==}
    engines: {node: '>=16'}

  asynckit@0.4.0:
    resolution: {integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==}

  bail@2.0.2:
    resolution: {integrity: sha512-0xO6mYd7JB2YesxDKplafRpsiOzPt9V02ddPCLbY1xYGPOX24NTyN50qnUxgCPcSoYMhKpAuBTjQoRZCAkUDRw==}

  blurhash@2.0.5:
    resolution: {integrity: sha512-cRygWd7kGBQO3VEhPiTgq4Wc43ctsM+o46urrmPOiuAe+07fzlSB9OJVdpgDL0jPqXUVQ9ht7aq7kxOeJHRK+w==}

  bowser@2.11.0:
    resolution: {integrity: sha512-AlcaJBi/pqqJBIQ8U9Mcpc9i8Aqxn88Skv5d+xBX006BY5u8N3mGLHa5Lgppa7L/HfwgwLgZ6NYs+Ag6uUmJRA==}

  call-bind-apply-helpers@1.0.2:
    resolution: {integrity: sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==}
    engines: {node: '>= 0.4'}

  ccount@2.0.1:
    resolution: {integrity: sha512-eyrF0jiFpY+3drT6383f1qhkbGsLSifNAjA61IUjZjmLCWjItY6LB9ft9YhoDgwfmclB2zhu51Lc7+95b8NRAg==}

  character-entities-html4@2.1.0:
    resolution: {integrity: sha512-1v7fgQRj6hnSwFpq1Eu0ynr/CDEw0rXo2B61qXrLNdHZmPKgb7fqS1a2JwF0rISo9q77jDI8VMEHoApn8qDoZA==}

  character-entities-legacy@3.0.0:
    resolution: {integrity: sha512-RpPp0asT/6ufRm//AJVwpViZbGM/MkjQFxJccQRHmISF/22NBtsHqAWmL+/pmkPWoIUJdWyeVleTl1wydHATVQ==}

  character-entities@2.0.2:
    resolution: {integrity: sha512-shx7oQ0Awen/BRIdkjkvz54PnEEI/EjwXDSIZp86/KKdbafHh1Df/RYGBhn4hbe2+uKC9FnT5UCEdyPz3ai9hQ==}

  ckeditor5-collaboration@46.0.0:
    resolution: {integrity: sha512-Cncgw3Rip76vpWwuJiES5UGaRFlOXdRiGac8jUzrnhFeV2pZA+sztD2yuOqthrwNa69X2zV/OQ20tKQvTjgn1g==}

  ckeditor5-premium-features@46.0.0:
    resolution: {integrity: sha512-K/08QmUlTQwtmfmQMpexIZWcxKRpWJJNgXRZO+9g9pdCFYhY6MbEJ6SdeWoPZZ2v6B2FJrvw1tQFkJ5TYn+vnA==}
    peerDependencies:
      ckeditor5: 46.0.0

  ckeditor5@46.0.0:
    resolution: {integrity: sha512-xOvTcZqeVCsEM6hp9kHtTAAS6Elie/WZ/RA3FYZjR2251CI66vNOb0GWU+c0ufr2Ha/FuXRdgV4nSpIv3JLvaA==}

  color-convert@3.1.0:
    resolution: {integrity: sha512-TVoqAq8ZDIpK5lsQY874DDnu65CSsc9vzq0wLpNQ6UMBq81GSZocVazPiBbYGzngzBOIRahpkTzCLVe2at4MfA==}
    engines: {node: '>=14.6'}

  color-name@2.0.0:
    resolution: {integrity: sha512-SbtvAMWvASO5TE2QP07jHBMXKafgdZz8Vrsrn96fiL+O92/FN/PLARzUW5sKt013fjAprK2d2iCn2hk2Xb5oow==}
    engines: {node: '>=12.20'}

  color-parse@2.0.2:
    resolution: {integrity: sha512-eCtOz5w5ttWIUcaKLiktF+DxZO1R9KLNY/xhbV6CkhM7sR3GhVghmt6X6yOnzeaM24po+Z9/S1apbXMwA3Iepw==}

  combined-stream@1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==}
    engines: {node: '>= 0.8'}

  comma-separated-tokens@2.0.3:
    resolution: {integrity: sha512-Fu4hJdvzeylCfQPp9SGWidpzrMs7tTrlu6Vb8XGaRGck8QSNZJJp538Wrb60Lax4fPwR64ViY468OIUTbRlGZg==}

  crelt@1.0.6:
    resolution: {integrity: sha512-VQ2MBenTq1fWZUH9DJNGti7kKv6EeAuYr3cLwxUWhIu1baTaXh4Ib5W2CqHVqib4/MqbYGJqiL3Zb8GJZr3l4g==}

  debug@4.3.7:
    resolution: {integrity: sha512-Er2nc/H7RrMXZBFCEim6TCmMk02Z8vLC2Rbi1KEBggpo0fS6l0S1nnapwmIi3yW/+GOJap1Krg4w0Hg80oCqgQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@4.4.1:
    resolution: {integrity: sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  decode-named-character-reference@1.2.0:
    resolution: {integrity: sha512-c6fcElNV6ShtZXmsgNgFFV5tVX2PaV4g+MOAkb8eXHvn6sryJBrZa9r0zV6+dtTyoCKxtDy5tyQ5ZwQuidtd+Q==}

  delayed-stream@1.0.0:
    resolution: {integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==}
    engines: {node: '>=0.4.0'}

  dequal@2.0.3:
    resolution: {integrity: sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA==}
    engines: {node: '>=6'}

  devlop@1.1.0:
    resolution: {integrity: sha512-RWmIqhcFf1lRYBvNmr7qTNuyCt/7/ns2jbpp1+PalgE/rDQcBT0fioSMUpJ93irlUhC5hrg4cYqe6U+0ImW0rA==}

  dunder-proto@1.0.1:
    resolution: {integrity: sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==}
    engines: {node: '>= 0.4'}

  engine.io-client@6.5.4:
    resolution: {integrity: sha512-GeZeeRjpD2qf49cZQ0Wvh/8NJNfeXkXXcoGh+F77oEAgo9gUHwT1fCRxSNU+YEEaysOJTnsFHmM5oAcPy4ntvQ==}

  engine.io-parser@5.2.3:
    resolution: {integrity: sha512-HqD3yTBfnBxIrbnM1DoD6Pcq8NECnh8d4As1Qgh0z5Gg3jRRIqijury0CL3ghu/edArpUYiYqQiDUQBIs4np3Q==}
    engines: {node: '>=10.0.0'}

  es-define-property@1.0.1:
    resolution: {integrity: sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==}
    engines: {node: '>= 0.4'}

  es-errors@1.3.0:
    resolution: {integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==}
    engines: {node: '>= 0.4'}

  es-object-atoms@1.1.1:
    resolution: {integrity: sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==}
    engines: {node: '>= 0.4'}

  es-set-tostringtag@2.1.0:
    resolution: {integrity: sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==}
    engines: {node: '>= 0.4'}

  es-toolkit@1.39.5:
    resolution: {integrity: sha512-z9V0qU4lx1TBXDNFWfAASWk6RNU6c6+TJBKE+FLIg8u0XJ6Yw58Hi0yX8ftEouj6p1QARRlXLFfHbIli93BdQQ==}

  esbuild@0.21.5:
    resolution: {integrity: sha512-mg3OPMV4hXywwpoDxu3Qda5xCKQi+vCTZq8S9J/EpkhB2HzKXq4SNFZE3+NK93JYxc8VMSep+lOUSC/RVKaBqw==}
    engines: {node: '>=12'}
    hasBin: true

  escape-string-regexp@5.0.0:
    resolution: {integrity: sha512-/veY75JbMK4j1yjvuUxuVsiS/hr/4iHs9FTT6cgTexxdE0Ly/glccBAkloH/DofkjRbZU3bnoj38mOmhkZ0lHw==}
    engines: {node: '>=12'}

  extend@3.0.2:
    resolution: {integrity: sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==}

  fast-xml-parser@4.4.1:
    resolution: {integrity: sha512-xkjOecfnKGkSsOwtZ5Pz7Us/T6mrbPQrq0nh+aCO5V9nk5NLWmasAHumTKjiPJPWANe+kAZ84Jc8ooJkzZ88Sw==}
    hasBin: true

  form-data@4.0.4:
    resolution: {integrity: sha512-KrGhL9Q4zjj0kiUt5OO4Mr/A/jlI2jDYs5eHBpYHPcBEVSiipAvn2Ko2HnPe20rmcuuvMHNdZFp+4IlGTMF0Ow==}
    engines: {node: '>= 6'}

  fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}

  fuzzysort@3.1.0:
    resolution: {integrity: sha512-sR9BNCjBg6LNgwvxlBd0sBABvQitkLzoVY9MYYROQVX/FvfJ4Mai9LsGhDgd8qYdds0bY77VzYd5iuB+v5rwQQ==}

  get-intrinsic@1.3.0:
    resolution: {integrity: sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==}
    engines: {node: '>= 0.4'}

  get-proto@1.0.1:
    resolution: {integrity: sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==}
    engines: {node: '>= 0.4'}

  gopd@1.2.0:
    resolution: {integrity: sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==}
    engines: {node: '>= 0.4'}

  has-symbols@1.1.0:
    resolution: {integrity: sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==}
    engines: {node: '>= 0.4'}

  has-tostringtag@1.0.2:
    resolution: {integrity: sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==}
    engines: {node: '>= 0.4'}

  hasown@2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==}
    engines: {node: '>= 0.4'}

  hast-util-embedded@3.0.0:
    resolution: {integrity: sha512-naH8sld4Pe2ep03qqULEtvYr7EjrLK2QHY8KJR6RJkTUjPGObe1vnx585uzem2hGra+s1q08DZZpfgDVYRbaXA==}

  hast-util-from-dom@5.0.1:
    resolution: {integrity: sha512-N+LqofjR2zuzTjCPzyDUdSshy4Ma6li7p/c3pA78uTwzFgENbgbUrm2ugwsOdcjI1muO+o6Dgzp9p8WHtn/39Q==}

  hast-util-has-property@3.0.0:
    resolution: {integrity: sha512-MNilsvEKLFpV604hwfhVStK0usFY/QmM5zX16bo7EjnAEGofr5YyI37kzopBlZJkHD4t887i+q/C8/tr5Q94cA==}

  hast-util-is-body-ok-link@3.0.1:
    resolution: {integrity: sha512-0qpnzOBLztXHbHQenVB8uNuxTnm/QBFUOmdOSsEn7GnBtyY07+ENTWVFBAnXd/zEgd9/SUG3lRY7hSIBWRgGpQ==}

  hast-util-is-element@3.0.0:
    resolution: {integrity: sha512-Val9mnv2IWpLbNPqc/pUem+a7Ipj2aHacCwgNfTiK0vJKl0LF+4Ba4+v1oPHFpf3bLYmreq0/l3Gud9S5OH42g==}

  hast-util-minify-whitespace@1.0.1:
    resolution: {integrity: sha512-L96fPOVpnclQE0xzdWb/D12VT5FabA7SnZOUMtL1DbXmYiHJMXZvFkIZfiMmTCNJHUeO2K9UYNXoVyfz+QHuOw==}

  hast-util-parse-selector@4.0.0:
    resolution: {integrity: sha512-wkQCkSYoOGCRKERFWcxMVMOcYE2K1AaNLU8DXS9arxnLOUEWbOXKXiJUNzEpqZ3JOKpnha3jkFrumEjVliDe7A==}

  hast-util-phrasing@3.0.1:
    resolution: {integrity: sha512-6h60VfI3uBQUxHqTyMymMZnEbNl1XmEGtOxxKYL7stY2o601COo62AWAYBQR9lZbYXYSBoxag8UpPRXK+9fqSQ==}

  hast-util-to-dom@4.0.1:
    resolution: {integrity: sha512-z1VE7sZ8uFzS2baF3LEflX1IPw2gSzrdo3QFEsyoi23MkCVY3FoE9x6nLgOgjwJu8VNWgo+07iaxtONhDzKrUQ==}

  hast-util-to-html@9.0.5:
    resolution: {integrity: sha512-OguPdidb+fbHQSU4Q4ZiLKnzWo8Wwsf5bZfbvu7//a9oTYoqD/fWpe96NuHkoS9h0ccGOTe0C4NGXdtS0iObOw==}

  hast-util-to-mdast@10.1.2:
    resolution: {integrity: sha512-FiCRI7NmOvM4y+f5w32jPRzcxDIz+PUqDwEqn1A+1q2cdp3B8Gx7aVrXORdOKjMNDQsD1ogOr896+0jJHW1EFQ==}

  hast-util-to-text@4.0.2:
    resolution: {integrity: sha512-KK6y/BN8lbaq654j7JgBydev7wuNMcID54lkRav1P0CaE1e47P72AWWPiGKXTJU271ooYzcvTAn/Zt0REnvc7A==}

  hast-util-whitespace@3.0.0:
    resolution: {integrity: sha512-88JUN06ipLwsnv+dVn+OIYOvAuvBMy/Qoi6O7mQHxdPXpjy+Cd6xRkWwux7DKO+4sYILtLBRIKgsdpS2gQc7qw==}

  hastscript@9.0.1:
    resolution: {integrity: sha512-g7df9rMFX/SPi34tyGCyUBREQoKkapwdY/T04Qn9TDWfHhAYt4/I0gMVirzK5wEzeUqIjEB+LXC/ypb7Aqno5w==}

  html-void-elements@3.0.0:
    resolution: {integrity: sha512-bEqo66MRXsUGxWHV5IP0PUiAWwoEjba4VCzg0LjFJBpchPaTfyfCKTG6bc5F8ucKec3q5y6qOdGyYTSBEvhCrg==}

  is-plain-obj@4.1.0:
    resolution: {integrity: sha512-+Pgi+vMuUNkJyExiMBt5IlFoMyKnr5zhJ4Uspz58WOhBF5QoIZkFyNHIbBAtHwzVAgk5RtndVNsDRN61/mmDqg==}
    engines: {node: '>=12'}

  keyux@0.7.2:
    resolution: {integrity: sha512-Z8ULf9BhSx1hI2rKG2uNjcvMgQmza97ZW2w43phS5VaT4wiTka7tOL4i/GJSc79k65tbvpoTVNCZwam0pqoH6A==}
    engines: {node: ^18.0.0 || >=20.0.0}

  long@5.3.2:
    resolution: {integrity: sha512-mNAgZ1GmyNhD7AuqnTG3/VQ26o760+ZYBPKjPvugO8+nLbYfX6TVpJPseBvopbdY+qpZ/lKUnmEc1LeZYS3QAA==}

  longest-streak@3.1.0:
    resolution: {integrity: sha512-9Ri+o0JYgehTaVBBDoMqIl8GXtbWg711O3srftcHhZ0dqnETqLaoIK0x17fUw9rFSlK/0NlsKe0Ahhyl5pXE2g==}

  luxon@3.6.1:
    resolution: {integrity: sha512-tJLxrKJhO2ukZ5z0gyjY1zPh3Rh88Ej9P7jNrZiHMUXHae1yvI2imgOZtL1TO8TW6biMMKfTtAOoEJANgtWBMQ==}
    engines: {node: '>=12'}

  markdown-table@3.0.4:
    resolution: {integrity: sha512-wiYz4+JrLyb/DqW2hkFJxP7Vd7JuTDm77fvbM8VfEQdmSMqcImWeeRbHwZjBjIFki/VaMK2BhFi7oUUZeM5bqw==}

  math-intrinsics@1.1.0:
    resolution: {integrity: sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==}
    engines: {node: '>= 0.4'}

  mdast-util-find-and-replace@3.0.2:
    resolution: {integrity: sha512-Tmd1Vg/m3Xz43afeNxDIhWRtFZgM2VLyaf4vSTYwudTyeuTneoL3qtWMA5jeLyz/O1vDJmmV4QuScFCA2tBPwg==}

  mdast-util-from-markdown@2.0.2:
    resolution: {integrity: sha512-uZhTV/8NBuw0WHkPTrCqDOl0zVe1BIng5ZtHoDk49ME1qqcjYmmLmOf0gELgcRMxN4w2iuIeVso5/6QymSrgmA==}

  mdast-util-gfm-autolink-literal@2.0.1:
    resolution: {integrity: sha512-5HVP2MKaP6L+G6YaxPNjuL0BPrq9orG3TsrZ9YXbA3vDw/ACI4MEsnoDpn6ZNm7GnZgtAcONJyPhOP8tNJQavQ==}

  mdast-util-gfm-footnote@2.1.0:
    resolution: {integrity: sha512-sqpDWlsHn7Ac9GNZQMeUzPQSMzR6Wv0WKRNvQRg0KqHh02fpTz69Qc1QSseNX29bhz1ROIyNyxExfawVKTm1GQ==}

  mdast-util-gfm-strikethrough@2.0.0:
    resolution: {integrity: sha512-mKKb915TF+OC5ptj5bJ7WFRPdYtuHv0yTRxK2tJvi+BDqbkiG7h7u/9SI89nRAYcmap2xHQL9D+QG/6wSrTtXg==}

  mdast-util-gfm-table@2.0.0:
    resolution: {integrity: sha512-78UEvebzz/rJIxLvE7ZtDd/vIQ0RHv+3Mh5DR96p7cS7HsBhYIICDBCu8csTNWNO6tBWfqXPWekRuj2FNOGOZg==}

  mdast-util-gfm-task-list-item@2.0.0:
    resolution: {integrity: sha512-IrtvNvjxC1o06taBAVJznEnkiHxLFTzgonUdy8hzFVeDun0uTjxxrRGVaNFqkU1wJR3RBPEfsxmU6jDWPofrTQ==}

  mdast-util-gfm@3.1.0:
    resolution: {integrity: sha512-0ulfdQOM3ysHhCJ1p06l0b0VKlhU0wuQs3thxZQagjcjPrlFRqY215uZGHHJan9GEAXd9MbfPjFJz+qMkVR6zQ==}

  mdast-util-newline-to-break@2.0.0:
    resolution: {integrity: sha512-MbgeFca0hLYIEx/2zGsszCSEJJ1JSCdiY5xQxRcLDDGa8EPvlLPupJ4DSajbMPAnC0je8jfb9TiUATnxxrHUog==}

  mdast-util-phrasing@4.1.0:
    resolution: {integrity: sha512-TqICwyvJJpBwvGAMZjj4J2n0X8QWp21b9l0o7eXyVJ25YNWYbJDVIyD1bZXE6WtV6RmKJVYmQAKWa0zWOABz2w==}

  mdast-util-to-hast@13.2.0:
    resolution: {integrity: sha512-QGYKEuUsYT9ykKBCMOEDLsU5JRObWQusAolFMeko/tYPufNkRffBAQjIE+99jbA87xv6FgmjLtwjh9wBWajwAA==}

  mdast-util-to-markdown@2.1.2:
    resolution: {integrity: sha512-xj68wMTvGXVOKonmog6LwyJKrYXZPvlwabaryTjLh9LuvovB/KAH+kvi8Gjj+7rJjsFi23nkUxRQv1KqSroMqA==}

  mdast-util-to-string@4.0.0:
    resolution: {integrity: sha512-0H44vDimn51F0YwvxSJSm0eCDOJTRlmN0R1yBh4HLj9wiV1Dn0QoXGbvFAWj2hSItVTlCmBF1hqKlIyUBVFLPg==}

  micromark-core-commonmark@2.0.3:
    resolution: {integrity: sha512-RDBrHEMSxVFLg6xvnXmb1Ayr2WzLAWjeSATAoxwKYJV94TeNavgoIdA0a9ytzDSVzBy2YKFK+emCPOEibLeCrg==}

  micromark-extension-gfm-autolink-literal@2.1.0:
    resolution: {integrity: sha512-oOg7knzhicgQ3t4QCjCWgTmfNhvQbDDnJeVu9v81r7NltNCVmhPy1fJRX27pISafdjL+SVc4d3l48Gb6pbRypw==}

  micromark-extension-gfm-footnote@2.1.0:
    resolution: {integrity: sha512-/yPhxI1ntnDNsiHtzLKYnE3vf9JZ6cAisqVDauhp4CEHxlb4uoOTxOCJ+9s51bIB8U1N1FJ1RXOKTIlD5B/gqw==}

  micromark-extension-gfm-strikethrough@2.1.0:
    resolution: {integrity: sha512-ADVjpOOkjz1hhkZLlBiYA9cR2Anf8F4HqZUO6e5eDcPQd0Txw5fxLzzxnEkSkfnD0wziSGiv7sYhk/ktvbf1uw==}

  micromark-extension-gfm-table@2.1.1:
    resolution: {integrity: sha512-t2OU/dXXioARrC6yWfJ4hqB7rct14e8f7m0cbI5hUmDyyIlwv5vEtooptH8INkbLzOatzKuVbQmAYcbWoyz6Dg==}

  micromark-extension-gfm-tagfilter@2.0.0:
    resolution: {integrity: sha512-xHlTOmuCSotIA8TW1mDIM6X2O1SiX5P9IuDtqGonFhEK0qgRI4yeC6vMxEV2dgyr2TiD+2PQ10o+cOhdVAcwfg==}

  micromark-extension-gfm-task-list-item@2.1.0:
    resolution: {integrity: sha512-qIBZhqxqI6fjLDYFTBIa4eivDMnP+OZqsNwmQ3xNLE4Cxwc+zfQEfbs6tzAo2Hjq+bh6q5F+Z8/cksrLFYWQQw==}

  micromark-extension-gfm@3.0.0:
    resolution: {integrity: sha512-vsKArQsicm7t0z2GugkCKtZehqUm31oeGBV/KVSorWSy8ZlNAv7ytjFhvaryUiCUJYqs+NoE6AFhpQvBTM6Q4w==}

  micromark-factory-destination@2.0.1:
    resolution: {integrity: sha512-Xe6rDdJlkmbFRExpTOmRj9N3MaWmbAgdpSrBQvCFqhezUn4AHqJHbaEnfbVYYiexVSs//tqOdY/DxhjdCiJnIA==}

  micromark-factory-label@2.0.1:
    resolution: {integrity: sha512-VFMekyQExqIW7xIChcXn4ok29YE3rnuyveW3wZQWWqF4Nv9Wk5rgJ99KzPvHjkmPXF93FXIbBp6YdW3t71/7Vg==}

  micromark-factory-space@2.0.1:
    resolution: {integrity: sha512-zRkxjtBxxLd2Sc0d+fbnEunsTj46SWXgXciZmHq0kDYGnck/ZSGj9/wULTV95uoeYiK5hRXP2mJ98Uo4cq/LQg==}

  micromark-factory-title@2.0.1:
    resolution: {integrity: sha512-5bZ+3CjhAd9eChYTHsjy6TGxpOFSKgKKJPJxr293jTbfry2KDoWkhBb6TcPVB4NmzaPhMs1Frm9AZH7OD4Cjzw==}

  micromark-factory-whitespace@2.0.1:
    resolution: {integrity: sha512-Ob0nuZ3PKt/n0hORHyvoD9uZhr+Za8sFoP+OnMcnWK5lngSzALgQYKMr9RJVOWLqQYuyn6ulqGWSXdwf6F80lQ==}

  micromark-util-character@2.1.1:
    resolution: {integrity: sha512-wv8tdUTJ3thSFFFJKtpYKOYiGP2+v96Hvk4Tu8KpCAsTMs6yi+nVmGh1syvSCsaxz45J6Jbw+9DD6g97+NV67Q==}

  micromark-util-chunked@2.0.1:
    resolution: {integrity: sha512-QUNFEOPELfmvv+4xiNg2sRYeS/P84pTW0TCgP5zc9FpXetHY0ab7SxKyAQCNCc1eK0459uoLI1y5oO5Vc1dbhA==}

  micromark-util-classify-character@2.0.1:
    resolution: {integrity: sha512-K0kHzM6afW/MbeWYWLjoHQv1sgg2Q9EccHEDzSkxiP/EaagNzCm7T/WMKZ3rjMbvIpvBiZgwR3dKMygtA4mG1Q==}

  micromark-util-combine-extensions@2.0.1:
    resolution: {integrity: sha512-OnAnH8Ujmy59JcyZw8JSbK9cGpdVY44NKgSM7E9Eh7DiLS2E9RNQf0dONaGDzEG9yjEl5hcqeIsj4hfRkLH/Bg==}

  micromark-util-decode-numeric-character-reference@2.0.2:
    resolution: {integrity: sha512-ccUbYk6CwVdkmCQMyr64dXz42EfHGkPQlBj5p7YVGzq8I7CtjXZJrubAYezf7Rp+bjPseiROqe7G6foFd+lEuw==}

  micromark-util-decode-string@2.0.1:
    resolution: {integrity: sha512-nDV/77Fj6eH1ynwscYTOsbK7rR//Uj0bZXBwJZRfaLEJ1iGBR6kIfNmlNqaqJf649EP0F3NWNdeJi03elllNUQ==}

  micromark-util-encode@2.0.1:
    resolution: {integrity: sha512-c3cVx2y4KqUnwopcO9b/SCdo2O67LwJJ/UyqGfbigahfegL9myoEFoDYZgkT7f36T0bLrM9hZTAaAyH+PCAXjw==}

  micromark-util-html-tag-name@2.0.1:
    resolution: {integrity: sha512-2cNEiYDhCWKI+Gs9T0Tiysk136SnR13hhO8yW6BGNyhOC4qYFnwF1nKfD3HFAIXA5c45RrIG1ub11GiXeYd1xA==}

  micromark-util-normalize-identifier@2.0.1:
    resolution: {integrity: sha512-sxPqmo70LyARJs0w2UclACPUUEqltCkJ6PhKdMIDuJ3gSf/Q+/GIe3WKl0Ijb/GyH9lOpUkRAO2wp0GVkLvS9Q==}

  micromark-util-resolve-all@2.0.1:
    resolution: {integrity: sha512-VdQyxFWFT2/FGJgwQnJYbe1jjQoNTS4RjglmSjTUlpUMa95Htx9NHeYW4rGDJzbjvCsl9eLjMQwGeElsqmzcHg==}

  micromark-util-sanitize-uri@2.0.1:
    resolution: {integrity: sha512-9N9IomZ/YuGGZZmQec1MbgxtlgougxTodVwDzzEouPKo3qFWvymFHWcnDi2vzV1ff6kas9ucW+o3yzJK9YB1AQ==}

  micromark-util-subtokenize@2.1.0:
    resolution: {integrity: sha512-XQLu552iSctvnEcgXw6+Sx75GflAPNED1qx7eBJ+wydBb2KCbRZe+NwvIEEMM83uml1+2WSXpBAcp9IUCgCYWA==}

  micromark-util-symbol@2.0.1:
    resolution: {integrity: sha512-vs5t8Apaud9N28kgCrRUdEed4UJ+wWNvicHLPxCa9ENlYuAY31M0ETy5y1vA33YoNPDFTghEbnh6efaE8h4x0Q==}

  micromark-util-types@2.0.2:
    resolution: {integrity: sha512-Yw0ECSpJoViF1qTU4DC6NwtC4aWGt1EkzaQB8KPPyCRR8z9TWeV0HbEFGTO+ZY1wB22zmxnJqhPyTpOVCpeHTA==}

  micromark@4.0.2:
    resolution: {integrity: sha512-zpe98Q6kvavpCr1NPVSCMebCKfD7CA2NqZ+rykeNhONIJBpc1tFKt9hucLGwha3jNTNI8lHpctWJWoimVF4PfA==}

  mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}

  mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}

  ms@2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}

  nanoid@3.3.11:
    resolution: {integrity: sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  picocolors@1.1.1:
    resolution: {integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==}

  postcss@8.5.6:
    resolution: {integrity: sha512-3Ybi1tAuwAP9s0r1UQ2J4n5Y0G05bJkpUIO0/bI9MhwmD70S5aTWbXGBwxHrelT+XM1k6dM0pk+SwNkpTRN7Pg==}
    engines: {node: ^10 || ^12 || >=14}

  property-information@7.1.0:
    resolution: {integrity: sha512-TwEZ+X+yCJmYfL7TPUOcvBZ4QfoT5YenQiJuX//0th53DE6w0xxLEtfK3iyryQFddXuvkIk51EEgrJQ0WJkOmQ==}

  protobufjs@7.5.0:
    resolution: {integrity: sha512-Z2E/kOY1QjoMlCytmexzYfDm/w5fKAiRwpSzGtdnXW1zC88Z2yXazHHrOtwCzn+7wSxyE8PYM4rvVcMphF9sOA==}
    engines: {node: '>=12.0.0'}

  querystringify@2.2.0:
    resolution: {integrity: sha512-FIqgj2EUvTa7R50u0rGsyTftzjYmv/a3hO345bZNrqabNqjtgiDMgmo4mkUjd+nzU5oF3dClKqFIPUKybUyqoQ==}

  rehype-dom-parse@5.0.2:
    resolution: {integrity: sha512-8CqP11KaqvtWsMqVEC2yM3cZWZsDNqqpr8nPvogjraLuh45stabgcpXadCAxu1n6JaUNJ/Xr3GIqXP7okbNqLg==}

  rehype-dom-stringify@4.0.2:
    resolution: {integrity: sha512-2HVFYbtmm5W3C2j8QsV9lcHdIMc2Yn/ytlPKcSC85/tRx2haZbU8V67Wxyh8STT38ZClvKlZ993Me/Hw8g88Aw==}

  rehype-minify-whitespace@6.0.2:
    resolution: {integrity: sha512-Zk0pyQ06A3Lyxhe9vGtOtzz3Z0+qZ5+7icZ/PL/2x1SHPbKao5oB/g/rlc6BCTajqBb33JcOe71Ye1oFsuYbnw==}

  rehype-remark@10.0.1:
    resolution: {integrity: sha512-EmDndlb5NVwXGfUa4c9GPK+lXeItTilLhE6ADSaQuHr4JUlKw9MidzGzx4HpqZrNCt6vnHmEifXQiiA+CEnjYQ==}

  remark-breaks@4.0.0:
    resolution: {integrity: sha512-IjEjJOkH4FuJvHZVIW0QCDWxcG96kCq7An/KVH2NfJe6rKZU2AsHeB3OEjPNRxi4QC34Xdx7I2KGYn6IpT7gxQ==}

  remark-gfm@4.0.1:
    resolution: {integrity: sha512-1quofZ2RQ9EWdeN34S79+KExV1764+wCUGop5CPL1WGdD0ocPpu91lzPGbwWMECpEpd42kJGQwzRfyov9j4yNg==}

  remark-parse@11.0.0:
    resolution: {integrity: sha512-FCxlKLNGknS5ba/1lmpYijMUzX2esxW5xQqjWxw2eHFfS2MSdaHVINFmhjo+qN1WhZhNimq0dZATN9pH0IDrpA==}

  remark-rehype@11.1.2:
    resolution: {integrity: sha512-Dh7l57ianaEoIpzbp0PC9UKAdCSVklD8E5Rpw7ETfbTl3FqcOOgq5q2LVDhgGCkaBv7p24JXikPdvhhmHvKMsw==}

  remark-stringify@11.0.0:
    resolution: {integrity: sha512-1OSmLd3awB/t8qdoEOMazZkNsfVTeY4fTsgzcQFdXNq8ToTN4ZGwrMnlda4K6smTFKD+GRV6O48i6Z4iKgPPpw==}

  requires-port@1.0.0:
    resolution: {integrity: sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==}

  rollup@4.46.2:
    resolution: {integrity: sha512-WMmLFI+Boh6xbop+OAGo9cQ3OgX9MIg7xOQjn+pTCwOkk+FNDAeAemXkJ3HzDJrVXleLOFVa1ipuc1AmEx1Dwg==}
    engines: {node: '>=18.0.0', npm: '>=8.0.0'}
    hasBin: true

  socket.io-client@4.7.0:
    resolution: {integrity: sha512-7Q8CeDrhuZzg4QLXl3tXlk5yb086oxYzehAVZRLiGCzCmtDneiHz1qHyyWcxhTgxXiokVpWQXoG/u60HoXSQew==}
    engines: {node: '>=10.0.0'}

  socket.io-parser@4.2.4:
    resolution: {integrity: sha512-/GbIKmo8ioc+NIWIhwdecY0ge+qVBSMdgxGygevmdHj24bsfgtCmcUUcQ5ZzcylGFHsN3k4HB4Cgkl96KVnuew==}
    engines: {node: '>=10.0.0'}

  source-map-js@1.2.1:
    resolution: {integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==}
    engines: {node: '>=0.10.0'}

  space-separated-tokens@2.0.2:
    resolution: {integrity: sha512-PEGlAwrG8yXGXRjW32fGbg66JAlOAwbObuqVoJpv/mRgoWDQfgH1wDPvtzWyUSNAXBGSk8h755YDbbcEy3SH2Q==}

  specificity@0.4.1:
    resolution: {integrity: sha512-1klA3Gi5PD1Wv9Q0wUoOQN1IWAuPu0D1U03ThXTr0cJ20+/iq2tHSDnK7Kk/0LXJ1ztUB2/1Os0wKmfyNgUQfg==}
    hasBin: true

  stringify-entities@4.0.4:
    resolution: {integrity: sha512-IwfBptatlO+QCJUo19AqvrPNqlVMpW9YEL2LIVY+Rpv2qsjCGxaDLNRgeGsQWJhfItebuJhsGSLjaBbNSQ+ieg==}

  strnum@1.1.2:
    resolution: {integrity: sha512-vrN+B7DBIoTTZjnPNewwhx6cBA/H+IS7rfW68n7XxC1y7uoiGQBxaKzqucGUgavX15dJgiGztLJ8vxuEzwqBdA==}

  style-mod@4.1.2:
    resolution: {integrity: sha512-wnD1HyVqpJUI2+eKZ+eo1UwghftP6yuFheBqqe+bWCotBjC2K1YnteJILRMs3SM4V/0dLEW1SC27MWP5y+mwmw==}

  trim-lines@3.0.1:
    resolution: {integrity: sha512-kRj8B+YHZCc9kQYdWfJB2/oUl9rA99qbowYYBtr4ui4mZyAQ2JpvVBd/6U2YloATfqBhBTSMhTpgBHtU0Mf3Rg==}

  trim-trailing-lines@2.1.0:
    resolution: {integrity: sha512-5UR5Biq4VlVOtzqkm2AZlgvSlDJtME46uV0br0gENbwN4l5+mMKT4b9gJKqWtuL2zAIqajGJGuvbCbcAJUZqBg==}

  trough@2.2.0:
    resolution: {integrity: sha512-tmMpK00BjZiUyVyvrBK7knerNgmgvcV/KLVyuma/SC+TQN167GrMRciANTz09+k3zW8L8t60jWO1GpfkZdjTaw==}

  tslib@2.8.1:
    resolution: {integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==}

  undici-types@7.8.0:
    resolution: {integrity: sha512-9UJ2xGDvQ43tYyVMpuHlsgApydB8ZKfVYTsLDhXkFL/6gfkp+U8xTGdh8pMJv1SpZna0zxG1DwsKZsreLbXBxw==}

  unified@11.0.5:
    resolution: {integrity: sha512-xKvGhPWw3k84Qjh8bI3ZeJjqnyadK+GEFtazSfZv/rKeTkTjOJho6mFqh2SM96iIcZokxiOpg78GazTSg8+KHA==}

  unist-util-find-after@5.0.0:
    resolution: {integrity: sha512-amQa0Ep2m6hE2g72AugUItjbuM8X8cGQnFoHk0pGfrFeT9GZhzN5SW8nRsiGKK7Aif4CrACPENkA6P/Lw6fHGQ==}

  unist-util-is@6.0.0:
    resolution: {integrity: sha512-2qCTHimwdxLfz+YzdGfkqNlH0tLi9xjTnHddPmJwtIG9MGsdbutfTc4P+haPD7l7Cjxf/WZj+we5qfVPvvxfYw==}

  unist-util-position@5.0.0:
    resolution: {integrity: sha512-fucsC7HjXvkB5R3kTCO7kUjRdrS0BJt3M/FPxmHMBOm8JQi2BsHAHFsy27E0EolP8rp0NzXsJ+jNPyDWvOJZPA==}

  unist-util-stringify-position@4.0.0:
    resolution: {integrity: sha512-0ASV06AAoKCDkS2+xw5RXJywruurpbC4JZSm7nr7MOt1ojAzvyyaO+UxZf18j8FCF6kmzCZKcAgN/yu2gm2XgQ==}

  unist-util-visit-parents@6.0.1:
    resolution: {integrity: sha512-L/PqWzfTP9lzzEa6CKs0k2nARxTdZduw3zyh8d2NVBnsyvHjSX4TWse388YrrQKbvI8w20fGjGlhgT96WwKykw==}

  unist-util-visit@5.0.0:
    resolution: {integrity: sha512-MR04uvD+07cwl/yhVuVWAtw+3GOR/knlL55Nd/wAdblk27GCVt3lqpTivy/tkJcZoNPzTwS1Y+KMojlLDhoTzg==}

  url-parse@1.5.10:
    resolution: {integrity: sha512-WypcfiRhfeUP9vvF0j6rw0J3hrWrw6iZv3+22h6iRMJ/8z1Tj6XfLP4DsUix5MhMPnXpiHDoKyoZ/bdCkwBCiQ==}

  uuid@9.0.1:
    resolution: {integrity: sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==}
    hasBin: true

  vanilla-colorful@0.7.2:
    resolution: {integrity: sha512-z2YZusTFC6KnLERx1cgoIRX2CjPRP0W75N+3CC6gbvdX5Ch47rZkEMGO2Xnf+IEmi3RiFLxS18gayMA27iU7Kg==}

  vfile-message@4.0.3:
    resolution: {integrity: sha512-QTHzsGd1EhbZs4AsQ20JX1rC3cOlt/IWJruk893DfLRr57lcnOeMaWG4K0JrRta4mIJZKth2Au3mM3u03/JWKw==}

  vfile@6.0.3:
    resolution: {integrity: sha512-KzIbH/9tXat2u30jf+smMwFCsno4wHVdNmzFyL+T/L3UGqqk6JKfVqOFOZEpZSHADH1k40ab6NUIXZq422ov3Q==}

  vite@5.4.19:
    resolution: {integrity: sha512-qO3aKv3HoQC8QKiNSTuUM1l9o/XX3+c+VTgLHbJWHZGeTPVAg2XwazI9UWzoxjIJCGCV2zU60uqMzjeLZuULqA==}
    engines: {node: ^18.0.0 || >=20.0.0}
    hasBin: true
    peerDependencies:
      '@types/node': ^18.0.0 || >=20.0.0
      less: '*'
      lightningcss: ^1.21.0
      sass: '*'
      sass-embedded: '*'
      stylus: '*'
      sugarss: '*'
      terser: ^5.4.0
    peerDependenciesMeta:
      '@types/node':
        optional: true
      less:
        optional: true
      lightningcss:
        optional: true
      sass:
        optional: true
      sass-embedded:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true

  w3c-keyname@2.2.8:
    resolution: {integrity: sha512-dpojBhNsCNN7T82Tm7k26A6G9ML3NkhDsnw9n/eoxSRlVBB4CEtIQ/KTCLI2Fwf3ataSXRhYFkQi3SlnFwPvPQ==}

  web-namespaces@2.0.1:
    resolution: {integrity: sha512-bKr1DkiNa2krS7qxNtdrtHAmzuYGFQLiQ13TsorsdT6ULTkPLKuu5+GsFpDlg6JFjUTwX2DyhMPG2be8uPrqsQ==}

  ws@8.17.1:
    resolution: {integrity: sha512-6XQFvXTkbfUOZOKKILFG1PDK2NDQs4azKQl26T0YS5CxqWLgXajbPZ+h4gZekJyRqFU8pvnbAbbs/3TgRPy+GQ==}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: '>=5.0.2'
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  ws@8.18.3:
    resolution: {integrity: sha512-PEIGCY5tSlUt50cqyMXfCzX+oOPqN0vuGqWzbcJ2xvnkzkq46oOpz7dQaTDBdfICb4N14+GARUDw2XV2N4tvzg==}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: '>=5.0.2'
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  xmlhttprequest-ssl@2.0.0:
    resolution: {integrity: sha512-QKxVRxiRACQcVuQEYFsI1hhkrMlrXHPegbbd1yn9UHOmRxY+si12nQYzri3vbzt8VdTTRviqcKxcyllFas5z2A==}
    engines: {node: '>=0.4.0'}

  zwitch@2.0.4:
    resolution: {integrity: sha512-bXE4cR/kVZhKZX/RjPEflHaKVhUVl85noU3v6b8apfQEc1x4A+zBxjZ4lN8LqGd6WZ3dl98pY4o717VFmoPp+A==}

snapshots:

  '@aws-crypto/crc32@5.2.0':
    dependencies:
      '@aws-crypto/util': 5.2.0
      '@aws-sdk/types': 3.821.0
      tslib: 2.8.1

  '@aws-crypto/sha256-browser@5.2.0':
    dependencies:
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-crypto/supports-web-crypto': 5.2.0
      '@aws-crypto/util': 5.2.0
      '@aws-sdk/types': 3.821.0
      '@aws-sdk/util-locate-window': 3.804.0
      '@smithy/util-utf8': 2.3.0
      tslib: 2.8.1

  '@aws-crypto/sha256-js@5.2.0':
    dependencies:
      '@aws-crypto/util': 5.2.0
      '@aws-sdk/types': 3.821.0
      tslib: 2.8.1

  '@aws-crypto/supports-web-crypto@5.2.0':
    dependencies:
      tslib: 2.8.1

  '@aws-crypto/util@5.2.0':
    dependencies:
      '@aws-sdk/types': 3.821.0
      '@smithy/util-utf8': 2.3.0
      tslib: 2.8.1

  '@aws-sdk/client-bedrock-runtime@3.823.0':
    dependencies:
      '@aws-crypto/sha256-browser': 5.2.0
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-sdk/core': 3.823.0
      '@aws-sdk/credential-provider-node': 3.823.0
      '@aws-sdk/eventstream-handler-node': 3.821.0
      '@aws-sdk/middleware-eventstream': 3.821.0
      '@aws-sdk/middleware-host-header': 3.821.0
      '@aws-sdk/middleware-logger': 3.821.0
      '@aws-sdk/middleware-recursion-detection': 3.821.0
      '@aws-sdk/middleware-user-agent': 3.823.0
      '@aws-sdk/region-config-resolver': 3.821.0
      '@aws-sdk/types': 3.821.0
      '@aws-sdk/util-endpoints': 3.821.0
      '@aws-sdk/util-user-agent-browser': 3.821.0
      '@aws-sdk/util-user-agent-node': 3.823.0
      '@smithy/config-resolver': 4.1.4
      '@smithy/core': 3.7.2
      '@smithy/eventstream-serde-browser': 4.0.4
      '@smithy/eventstream-serde-config-resolver': 4.1.2
      '@smithy/eventstream-serde-node': 4.0.4
      '@smithy/fetch-http-handler': 5.1.0
      '@smithy/hash-node': 4.0.4
      '@smithy/invalid-dependency': 4.0.4
      '@smithy/middleware-content-length': 4.0.4
      '@smithy/middleware-endpoint': 4.1.17
      '@smithy/middleware-retry': 4.1.18
      '@smithy/middleware-serde': 4.0.8
      '@smithy/middleware-stack': 4.0.4
      '@smithy/node-config-provider': 4.1.3
      '@smithy/node-http-handler': 4.1.0
      '@smithy/protocol-http': 5.1.2
      '@smithy/smithy-client': 4.4.9
      '@smithy/types': 4.3.1
      '@smithy/url-parser': 4.0.4
      '@smithy/util-base64': 4.0.0
      '@smithy/util-body-length-browser': 4.0.0
      '@smithy/util-body-length-node': 4.0.0
      '@smithy/util-defaults-mode-browser': 4.0.25
      '@smithy/util-defaults-mode-node': 4.0.25
      '@smithy/util-endpoints': 3.0.6
      '@smithy/util-middleware': 4.0.4
      '@smithy/util-retry': 4.0.6
      '@smithy/util-stream': 4.2.3
      '@smithy/util-utf8': 4.0.0
      '@types/uuid': 9.0.8
      tslib: 2.8.1
      uuid: 9.0.1
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/client-sso@3.823.0':
    dependencies:
      '@aws-crypto/sha256-browser': 5.2.0
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-sdk/core': 3.823.0
      '@aws-sdk/middleware-host-header': 3.821.0
      '@aws-sdk/middleware-logger': 3.821.0
      '@aws-sdk/middleware-recursion-detection': 3.821.0
      '@aws-sdk/middleware-user-agent': 3.823.0
      '@aws-sdk/region-config-resolver': 3.821.0
      '@aws-sdk/types': 3.821.0
      '@aws-sdk/util-endpoints': 3.821.0
      '@aws-sdk/util-user-agent-browser': 3.821.0
      '@aws-sdk/util-user-agent-node': 3.823.0
      '@smithy/config-resolver': 4.1.4
      '@smithy/core': 3.7.2
      '@smithy/fetch-http-handler': 5.1.0
      '@smithy/hash-node': 4.0.4
      '@smithy/invalid-dependency': 4.0.4
      '@smithy/middleware-content-length': 4.0.4
      '@smithy/middleware-endpoint': 4.1.17
      '@smithy/middleware-retry': 4.1.18
      '@smithy/middleware-serde': 4.0.8
      '@smithy/middleware-stack': 4.0.4
      '@smithy/node-config-provider': 4.1.3
      '@smithy/node-http-handler': 4.1.0
      '@smithy/protocol-http': 5.1.2
      '@smithy/smithy-client': 4.4.9
      '@smithy/types': 4.3.1
      '@smithy/url-parser': 4.0.4
      '@smithy/util-base64': 4.0.0
      '@smithy/util-body-length-browser': 4.0.0
      '@smithy/util-body-length-node': 4.0.0
      '@smithy/util-defaults-mode-browser': 4.0.25
      '@smithy/util-defaults-mode-node': 4.0.25
      '@smithy/util-endpoints': 3.0.6
      '@smithy/util-middleware': 4.0.4
      '@smithy/util-retry': 4.0.6
      '@smithy/util-utf8': 4.0.0
      tslib: 2.8.1
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/core@3.823.0':
    dependencies:
      '@aws-sdk/types': 3.821.0
      '@aws-sdk/xml-builder': 3.821.0
      '@smithy/core': 3.7.2
      '@smithy/node-config-provider': 4.1.3
      '@smithy/property-provider': 4.0.4
      '@smithy/protocol-http': 5.1.2
      '@smithy/signature-v4': 5.1.2
      '@smithy/smithy-client': 4.4.9
      '@smithy/types': 4.3.1
      '@smithy/util-base64': 4.0.0
      '@smithy/util-body-length-browser': 4.0.0
      '@smithy/util-middleware': 4.0.4
      '@smithy/util-utf8': 4.0.0
      fast-xml-parser: 4.4.1
      tslib: 2.8.1

  '@aws-sdk/credential-provider-env@3.823.0':
    dependencies:
      '@aws-sdk/core': 3.823.0
      '@aws-sdk/types': 3.821.0
      '@smithy/property-provider': 4.0.4
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@aws-sdk/credential-provider-http@3.823.0':
    dependencies:
      '@aws-sdk/core': 3.823.0
      '@aws-sdk/types': 3.821.0
      '@smithy/fetch-http-handler': 5.1.0
      '@smithy/node-http-handler': 4.1.0
      '@smithy/property-provider': 4.0.4
      '@smithy/protocol-http': 5.1.2
      '@smithy/smithy-client': 4.4.9
      '@smithy/types': 4.3.1
      '@smithy/util-stream': 4.2.3
      tslib: 2.8.1

  '@aws-sdk/credential-provider-ini@3.823.0':
    dependencies:
      '@aws-sdk/core': 3.823.0
      '@aws-sdk/credential-provider-env': 3.823.0
      '@aws-sdk/credential-provider-http': 3.823.0
      '@aws-sdk/credential-provider-process': 3.823.0
      '@aws-sdk/credential-provider-sso': 3.823.0
      '@aws-sdk/credential-provider-web-identity': 3.823.0
      '@aws-sdk/nested-clients': 3.823.0
      '@aws-sdk/types': 3.821.0
      '@smithy/credential-provider-imds': 4.0.6
      '@smithy/property-provider': 4.0.4
      '@smithy/shared-ini-file-loader': 4.0.4
      '@smithy/types': 4.3.1
      tslib: 2.8.1
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/credential-provider-node@3.823.0':
    dependencies:
      '@aws-sdk/credential-provider-env': 3.823.0
      '@aws-sdk/credential-provider-http': 3.823.0
      '@aws-sdk/credential-provider-ini': 3.823.0
      '@aws-sdk/credential-provider-process': 3.823.0
      '@aws-sdk/credential-provider-sso': 3.823.0
      '@aws-sdk/credential-provider-web-identity': 3.823.0
      '@aws-sdk/types': 3.821.0
      '@smithy/credential-provider-imds': 4.0.6
      '@smithy/property-provider': 4.0.4
      '@smithy/shared-ini-file-loader': 4.0.4
      '@smithy/types': 4.3.1
      tslib: 2.8.1
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/credential-provider-process@3.823.0':
    dependencies:
      '@aws-sdk/core': 3.823.0
      '@aws-sdk/types': 3.821.0
      '@smithy/property-provider': 4.0.4
      '@smithy/shared-ini-file-loader': 4.0.4
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@aws-sdk/credential-provider-sso@3.823.0':
    dependencies:
      '@aws-sdk/client-sso': 3.823.0
      '@aws-sdk/core': 3.823.0
      '@aws-sdk/token-providers': 3.823.0
      '@aws-sdk/types': 3.821.0
      '@smithy/property-provider': 4.0.4
      '@smithy/shared-ini-file-loader': 4.0.4
      '@smithy/types': 4.3.1
      tslib: 2.8.1
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/credential-provider-web-identity@3.823.0':
    dependencies:
      '@aws-sdk/core': 3.823.0
      '@aws-sdk/nested-clients': 3.823.0
      '@aws-sdk/types': 3.821.0
      '@smithy/property-provider': 4.0.4
      '@smithy/types': 4.3.1
      tslib: 2.8.1
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/eventstream-handler-node@3.821.0':
    dependencies:
      '@aws-sdk/types': 3.821.0
      '@smithy/eventstream-codec': 4.0.4
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@aws-sdk/middleware-eventstream@3.821.0':
    dependencies:
      '@aws-sdk/types': 3.821.0
      '@smithy/protocol-http': 5.1.2
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@aws-sdk/middleware-host-header@3.821.0':
    dependencies:
      '@aws-sdk/types': 3.821.0
      '@smithy/protocol-http': 5.1.2
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@aws-sdk/middleware-logger@3.821.0':
    dependencies:
      '@aws-sdk/types': 3.821.0
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@aws-sdk/middleware-recursion-detection@3.821.0':
    dependencies:
      '@aws-sdk/types': 3.821.0
      '@smithy/protocol-http': 5.1.2
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@aws-sdk/middleware-user-agent@3.823.0':
    dependencies:
      '@aws-sdk/core': 3.823.0
      '@aws-sdk/types': 3.821.0
      '@aws-sdk/util-endpoints': 3.821.0
      '@smithy/core': 3.7.2
      '@smithy/protocol-http': 5.1.2
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@aws-sdk/nested-clients@3.823.0':
    dependencies:
      '@aws-crypto/sha256-browser': 5.2.0
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-sdk/core': 3.823.0
      '@aws-sdk/middleware-host-header': 3.821.0
      '@aws-sdk/middleware-logger': 3.821.0
      '@aws-sdk/middleware-recursion-detection': 3.821.0
      '@aws-sdk/middleware-user-agent': 3.823.0
      '@aws-sdk/region-config-resolver': 3.821.0
      '@aws-sdk/types': 3.821.0
      '@aws-sdk/util-endpoints': 3.821.0
      '@aws-sdk/util-user-agent-browser': 3.821.0
      '@aws-sdk/util-user-agent-node': 3.823.0
      '@smithy/config-resolver': 4.1.4
      '@smithy/core': 3.7.2
      '@smithy/fetch-http-handler': 5.1.0
      '@smithy/hash-node': 4.0.4
      '@smithy/invalid-dependency': 4.0.4
      '@smithy/middleware-content-length': 4.0.4
      '@smithy/middleware-endpoint': 4.1.17
      '@smithy/middleware-retry': 4.1.18
      '@smithy/middleware-serde': 4.0.8
      '@smithy/middleware-stack': 4.0.4
      '@smithy/node-config-provider': 4.1.3
      '@smithy/node-http-handler': 4.1.0
      '@smithy/protocol-http': 5.1.2
      '@smithy/smithy-client': 4.4.9
      '@smithy/types': 4.3.1
      '@smithy/url-parser': 4.0.4
      '@smithy/util-base64': 4.0.0
      '@smithy/util-body-length-browser': 4.0.0
      '@smithy/util-body-length-node': 4.0.0
      '@smithy/util-defaults-mode-browser': 4.0.25
      '@smithy/util-defaults-mode-node': 4.0.25
      '@smithy/util-endpoints': 3.0.6
      '@smithy/util-middleware': 4.0.4
      '@smithy/util-retry': 4.0.6
      '@smithy/util-utf8': 4.0.0
      tslib: 2.8.1
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/region-config-resolver@3.821.0':
    dependencies:
      '@aws-sdk/types': 3.821.0
      '@smithy/node-config-provider': 4.1.3
      '@smithy/types': 4.3.1
      '@smithy/util-config-provider': 4.0.0
      '@smithy/util-middleware': 4.0.4
      tslib: 2.8.1

  '@aws-sdk/token-providers@3.823.0':
    dependencies:
      '@aws-sdk/core': 3.823.0
      '@aws-sdk/nested-clients': 3.823.0
      '@aws-sdk/types': 3.821.0
      '@smithy/property-provider': 4.0.4
      '@smithy/shared-ini-file-loader': 4.0.4
      '@smithy/types': 4.3.1
      tslib: 2.8.1
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/types@3.821.0':
    dependencies:
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@aws-sdk/util-endpoints@3.821.0':
    dependencies:
      '@aws-sdk/types': 3.821.0
      '@smithy/types': 4.3.1
      '@smithy/util-endpoints': 3.0.6
      tslib: 2.8.1

  '@aws-sdk/util-locate-window@3.804.0':
    dependencies:
      tslib: 2.8.1

  '@aws-sdk/util-user-agent-browser@3.821.0':
    dependencies:
      '@aws-sdk/types': 3.821.0
      '@smithy/types': 4.3.1
      bowser: 2.11.0
      tslib: 2.8.1

  '@aws-sdk/util-user-agent-node@3.823.0':
    dependencies:
      '@aws-sdk/middleware-user-agent': 3.823.0
      '@aws-sdk/types': 3.821.0
      '@smithy/node-config-provider': 4.1.3
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@aws-sdk/xml-builder@3.821.0':
    dependencies:
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@ckeditor/ckeditor-cloud-services-collaboration@53.0.0(@ckeditor/ckeditor5-utils@46.0.0)(ckeditor5@46.0.0)':
    dependencies:
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0
      protobufjs: 7.5.0
      socket.io-client: 4.7.0
      socket.io-parser: 4.2.4
      url-parse: 1.5.10
      uuid: 9.0.1
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  '@ckeditor/ckeditor5-adapter-ckfinder@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-upload': 46.0.0
      ckeditor5: 46.0.0
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-ai@46.0.0':
    dependencies:
      '@aws-sdk/client-bedrock-runtime': 3.823.0
      '@ckeditor/ckeditor5-clipboard': 46.0.0
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-table': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0
      es-toolkit: 1.39.5
    transitivePeerDependencies:
      - aws-crt

  '@ckeditor/ckeditor5-alignment@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-autoformat@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-heading': 46.0.0
      '@ckeditor/ckeditor5-typing': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-autosave@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0
      es-toolkit: 1.39.5
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-basic-styles@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-typing': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-block-quote@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-enter': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-typing': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-bookmark@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-link': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      '@ckeditor/ckeditor5-widget': 46.0.0
      ckeditor5: 46.0.0
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-case-change@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0

  '@ckeditor/ckeditor5-ckbox@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-cloud-services': 46.0.0
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-image': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-upload': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      blurhash: 2.0.5
      ckeditor5: 46.0.0
      es-toolkit: 1.39.5
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-ckfinder@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-image': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-clipboard@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      '@ckeditor/ckeditor5-widget': 46.0.0
      es-toolkit: 1.39.5

  '@ckeditor/ckeditor5-cloud-services@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-code-block@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-clipboard': 46.0.0
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-enter': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-collaboration-core@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-comments': 46.0.0
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-theme-lark': 46.0.0
      '@ckeditor/ckeditor5-track-changes': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      '@types/luxon': 3.6.2
      ckeditor5: 46.0.0
      luxon: 3.6.1
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-comments@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-clipboard': 46.0.0
      '@ckeditor/ckeditor5-collaboration-core': 46.0.0
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-enter': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-paragraph': 46.0.0
      '@ckeditor/ckeditor5-select-all': 46.0.0
      '@ckeditor/ckeditor5-source-editing': 46.0.0
      '@ckeditor/ckeditor5-theme-lark': 46.0.0
      '@ckeditor/ckeditor5-typing': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-undo': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      '@ckeditor/ckeditor5-widget': 46.0.0
      ckeditor5: 46.0.0
      ckeditor5-collaboration: 46.0.0
      es-toolkit: 1.39.5
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-core@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      '@ckeditor/ckeditor5-watchdog': 46.0.0
      es-toolkit: 1.39.5

  '@ckeditor/ckeditor5-document-outline@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-heading': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      '@ckeditor/ckeditor5-widget': 46.0.0
      ckeditor5: 46.0.0
      es-toolkit: 1.39.5

  '@ckeditor/ckeditor5-easy-image@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-cloud-services': 46.0.0
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-upload': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-editor-balloon@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0
      es-toolkit: 1.39.5
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-editor-classic@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0
      es-toolkit: 1.39.5
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-editor-decoupled@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0
      es-toolkit: 1.39.5
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-editor-inline@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0
      es-toolkit: 1.39.5
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-editor-multi-root@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0
      es-toolkit: 1.39.5
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-email@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-export-inline-styles': 46.0.0
      '@ckeditor/ckeditor5-font': 46.0.0
      '@ckeditor/ckeditor5-html-support': 46.0.0
      '@ckeditor/ckeditor5-list': 46.0.0
      '@ckeditor/ckeditor5-table': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0

  '@ckeditor/ckeditor5-emoji@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-mention': 46.0.0
      '@ckeditor/ckeditor5-typing': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0
      es-toolkit: 1.39.5
      fuzzysort: 3.1.0
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-engine@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-utils': 46.0.0
      es-toolkit: 1.39.5

  '@ckeditor/ckeditor5-enter@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0

  '@ckeditor/ckeditor5-essentials@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-clipboard': 46.0.0
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-enter': 46.0.0
      '@ckeditor/ckeditor5-select-all': 46.0.0
      '@ckeditor/ckeditor5-typing': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-undo': 46.0.0
      ckeditor5: 46.0.0
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-export-inline-styles@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0
      specificity: 0.4.1

  '@ckeditor/ckeditor5-export-pdf@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-cloud-services': 46.0.0
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-merge-fields': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0

  '@ckeditor/ckeditor5-export-word@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-cloud-services': 46.0.0
      '@ckeditor/ckeditor5-collaboration-core': 46.0.0
      '@ckeditor/ckeditor5-comments': 46.0.0
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-merge-fields': 46.0.0
      '@ckeditor/ckeditor5-track-changes': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-find-and-replace@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0
      es-toolkit: 1.39.5
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-font@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-format-painter@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0

  '@ckeditor/ckeditor5-fullscreen@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-editor-classic': 46.0.0
      '@ckeditor/ckeditor5-editor-decoupled': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-heading@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-paragraph': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-highlight@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-horizontal-line@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      '@ckeditor/ckeditor5-widget': 46.0.0
      ckeditor5: 46.0.0
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-html-embed@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      '@ckeditor/ckeditor5-widget': 46.0.0
      ckeditor5: 46.0.0
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-html-support@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-enter': 46.0.0
      '@ckeditor/ckeditor5-heading': 46.0.0
      '@ckeditor/ckeditor5-image': 46.0.0
      '@ckeditor/ckeditor5-list': 46.0.0
      '@ckeditor/ckeditor5-remove-format': 46.0.0
      '@ckeditor/ckeditor5-table': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      '@ckeditor/ckeditor5-widget': 46.0.0
      ckeditor5: 46.0.0
      es-toolkit: 1.39.5
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-icons@46.0.0': {}

  '@ckeditor/ckeditor5-image@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-clipboard': 46.0.0
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-typing': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-undo': 46.0.0
      '@ckeditor/ckeditor5-upload': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      '@ckeditor/ckeditor5-widget': 46.0.0
      ckeditor5: 46.0.0
      es-toolkit: 1.39.5
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-import-word@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-clipboard': 46.0.0
      '@ckeditor/ckeditor5-cloud-services': 46.0.0
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-merge-fields': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0

  '@ckeditor/ckeditor5-indent@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-heading': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-list': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-language@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-line-height@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0

  '@ckeditor/ckeditor5-link@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-clipboard': 46.0.0
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-image': 46.0.0
      '@ckeditor/ckeditor5-typing': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      '@ckeditor/ckeditor5-widget': 46.0.0
      ckeditor5: 46.0.0
      es-toolkit: 1.39.5
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-list-multi-level@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-list': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0

  '@ckeditor/ckeditor5-list@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-clipboard': 46.0.0
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-enter': 46.0.0
      '@ckeditor/ckeditor5-font': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-typing': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-markdown-gfm@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-clipboard': 46.0.0
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@types/hast': 3.0.4
      ckeditor5: 46.0.0
      hast-util-from-dom: 5.0.1
      hast-util-to-html: 9.0.5
      hast-util-to-mdast: 10.1.2
      hastscript: 9.0.1
      rehype-dom-parse: 5.0.2
      rehype-dom-stringify: 4.0.2
      rehype-remark: 10.0.1
      remark-breaks: 4.0.0
      remark-gfm: 4.0.1
      remark-parse: 11.0.0
      remark-rehype: 11.1.2
      remark-stringify: 11.0.0
      unified: 11.0.5
      unist-util-visit: 5.0.0
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-media-embed@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-clipboard': 46.0.0
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-typing': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-undo': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      '@ckeditor/ckeditor5-widget': 46.0.0
      ckeditor5: 46.0.0

  '@ckeditor/ckeditor5-mention@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-typing': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0
      es-toolkit: 1.39.5

  '@ckeditor/ckeditor5-merge-fields@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-image': 46.0.0
      '@ckeditor/ckeditor5-mention': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      '@ckeditor/ckeditor5-widget': 46.0.0
      ckeditor5: 46.0.0
      es-toolkit: 1.39.5

  '@ckeditor/ckeditor5-minimap@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-operations-compressor@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0
      es-toolkit: 1.39.5
      protobufjs: 7.5.0

  '@ckeditor/ckeditor5-page-break@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      '@ckeditor/ckeditor5-widget': 46.0.0
      ckeditor5: 46.0.0

  '@ckeditor/ckeditor5-pagination@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-theme-lark': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0
      es-toolkit: 1.39.5

  '@ckeditor/ckeditor5-paragraph@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0

  '@ckeditor/ckeditor5-paste-from-office-enhanced@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-paste-from-office': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0

  '@ckeditor/ckeditor5-paste-from-office@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-clipboard': 46.0.0
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      ckeditor5: 46.0.0

  '@ckeditor/ckeditor5-real-time-collaboration@46.0.0':
    dependencies:
      '@ckeditor/ckeditor-cloud-services-collaboration': 53.0.0(@ckeditor/ckeditor5-utils@46.0.0)(ckeditor5@46.0.0)
      '@ckeditor/ckeditor5-cloud-services': 46.0.0
      '@ckeditor/ckeditor5-comments': 46.0.0
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-editor-multi-root': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-operations-compressor': 46.0.0
      '@ckeditor/ckeditor5-revision-history': 46.0.0
      '@ckeditor/ckeditor5-theme-lark': 46.0.0
      '@ckeditor/ckeditor5-track-changes': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0
      ckeditor5-collaboration: 46.0.0
      es-toolkit: 1.39.5
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  '@ckeditor/ckeditor5-remove-format@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0

  '@ckeditor/ckeditor5-restricted-editing@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0

  '@ckeditor/ckeditor5-revision-history@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-autosave': 46.0.0
      '@ckeditor/ckeditor5-comments': 46.0.0
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-editor-classic': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      '@types/luxon': 3.6.2
      ckeditor5: 46.0.0
      ckeditor5-collaboration: 46.0.0
      es-toolkit: 1.39.5
      luxon: 3.6.1
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-select-all@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0

  '@ckeditor/ckeditor5-show-blocks@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0

  '@ckeditor/ckeditor5-slash-command@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-heading': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-mention': 46.0.0
      '@ckeditor/ckeditor5-style': 46.0.0
      '@ckeditor/ckeditor5-template': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0

  '@ckeditor/ckeditor5-source-editing-enhanced@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-theme-lark': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      '@codemirror/autocomplete': 6.18.6
      '@codemirror/commands': 6.8.1
      '@codemirror/lang-html': 6.4.9
      '@codemirror/lang-markdown': 6.3.2
      '@codemirror/language': 6.11.2
      '@codemirror/state': 6.5.2
      '@codemirror/theme-one-dark': 6.1.3
      '@codemirror/view': 6.38.1
      ckeditor5: 46.0.0

  '@ckeditor/ckeditor5-source-editing@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-theme-lark': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0

  '@ckeditor/ckeditor5-special-characters@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-typing': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0

  '@ckeditor/ckeditor5-style@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-html-support': 46.0.0
      '@ckeditor/ckeditor5-list': 46.0.0
      '@ckeditor/ckeditor5-table': 46.0.0
      '@ckeditor/ckeditor5-typing': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0
      es-toolkit: 1.39.5

  '@ckeditor/ckeditor5-table@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-clipboard': 46.0.0
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      '@ckeditor/ckeditor5-widget': 46.0.0
      ckeditor5: 46.0.0
      es-toolkit: 1.39.5

  '@ckeditor/ckeditor5-template@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0

  '@ckeditor/ckeditor5-theme-lark@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-ui': 46.0.0

  '@ckeditor/ckeditor5-track-changes@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-clipboard': 46.0.0
      '@ckeditor/ckeditor5-code-block': 46.0.0
      '@ckeditor/ckeditor5-comments': 46.0.0
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-editor-multi-root': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-enter': 46.0.0
      '@ckeditor/ckeditor5-find-and-replace': 46.0.0
      '@ckeditor/ckeditor5-font': 46.0.0
      '@ckeditor/ckeditor5-heading': 46.0.0
      '@ckeditor/ckeditor5-highlight': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-image': 46.0.0
      '@ckeditor/ckeditor5-link': 46.0.0
      '@ckeditor/ckeditor5-list': 46.0.0
      '@ckeditor/ckeditor5-media-embed': 46.0.0
      '@ckeditor/ckeditor5-merge-fields': 46.0.0
      '@ckeditor/ckeditor5-restricted-editing': 46.0.0
      '@ckeditor/ckeditor5-style': 46.0.0
      '@ckeditor/ckeditor5-table': 46.0.0
      '@ckeditor/ckeditor5-typing': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      '@ckeditor/ckeditor5-widget': 46.0.0
      ckeditor5: 46.0.0
      ckeditor5-collaboration: 46.0.0
      es-toolkit: 1.39.5
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-typing@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      es-toolkit: 1.39.5

  '@ckeditor/ckeditor5-ui@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-editor-multi-root': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      '@types/color-convert': 2.0.4
      color-convert: 3.1.0
      color-parse: 2.0.2
      es-toolkit: 1.39.5
      vanilla-colorful: 0.7.2
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-undo@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0

  '@ckeditor/ckeditor5-upload@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0

  '@ckeditor/ckeditor5-uploadcare@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-image': 46.0.0
      '@ckeditor/ckeditor5-theme-lark': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-upload': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      '@uploadcare/file-uploader': 1.16.3-alpha.0
      '@uploadcare/upload-client': 6.14.3
      ckeditor5: 46.0.0
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate

  '@ckeditor/ckeditor5-utils@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-ui': 46.0.0
      es-toolkit: 1.39.5
    transitivePeerDependencies:
      - supports-color

  '@ckeditor/ckeditor5-watchdog@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-editor-multi-root': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      es-toolkit: 1.39.5

  '@ckeditor/ckeditor5-widget@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-enter': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-typing': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      es-toolkit: 1.39.5

  '@ckeditor/ckeditor5-word-count@46.0.0':
    dependencies:
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0
      es-toolkit: 1.39.5

  '@codemirror/autocomplete@6.18.6':
    dependencies:
      '@codemirror/language': 6.11.2
      '@codemirror/state': 6.5.2
      '@codemirror/view': 6.38.1
      '@lezer/common': 1.2.3

  '@codemirror/commands@6.8.1':
    dependencies:
      '@codemirror/language': 6.11.2
      '@codemirror/state': 6.5.2
      '@codemirror/view': 6.38.1
      '@lezer/common': 1.2.3

  '@codemirror/lang-css@6.3.1':
    dependencies:
      '@codemirror/autocomplete': 6.18.6
      '@codemirror/language': 6.11.2
      '@codemirror/state': 6.5.2
      '@lezer/common': 1.2.3
      '@lezer/css': 1.3.0

  '@codemirror/lang-html@6.4.9':
    dependencies:
      '@codemirror/autocomplete': 6.18.6
      '@codemirror/lang-css': 6.3.1
      '@codemirror/lang-javascript': 6.2.4
      '@codemirror/language': 6.11.2
      '@codemirror/state': 6.5.2
      '@codemirror/view': 6.38.1
      '@lezer/common': 1.2.3
      '@lezer/css': 1.3.0
      '@lezer/html': 1.3.10

  '@codemirror/lang-javascript@6.2.4':
    dependencies:
      '@codemirror/autocomplete': 6.18.6
      '@codemirror/language': 6.11.2
      '@codemirror/lint': 6.8.5
      '@codemirror/state': 6.5.2
      '@codemirror/view': 6.38.1
      '@lezer/common': 1.2.3
      '@lezer/javascript': 1.5.1

  '@codemirror/lang-markdown@6.3.2':
    dependencies:
      '@codemirror/autocomplete': 6.18.6
      '@codemirror/lang-html': 6.4.9
      '@codemirror/language': 6.11.2
      '@codemirror/state': 6.5.2
      '@codemirror/view': 6.38.1
      '@lezer/common': 1.2.3
      '@lezer/markdown': 1.4.3

  '@codemirror/language@6.11.2':
    dependencies:
      '@codemirror/state': 6.5.2
      '@codemirror/view': 6.38.1
      '@lezer/common': 1.2.3
      '@lezer/highlight': 1.2.1
      '@lezer/lr': 1.4.2
      style-mod: 4.1.2

  '@codemirror/lint@6.8.5':
    dependencies:
      '@codemirror/state': 6.5.2
      '@codemirror/view': 6.38.1
      crelt: 1.0.6

  '@codemirror/state@6.5.2':
    dependencies:
      '@marijn/find-cluster-break': 1.0.2

  '@codemirror/theme-one-dark@6.1.3':
    dependencies:
      '@codemirror/language': 6.11.2
      '@codemirror/state': 6.5.2
      '@codemirror/view': 6.38.1
      '@lezer/highlight': 1.2.1

  '@codemirror/view@6.38.1':
    dependencies:
      '@codemirror/state': 6.5.2
      crelt: 1.0.6
      style-mod: 4.1.2
      w3c-keyname: 2.2.8

  '@esbuild/aix-ppc64@0.21.5':
    optional: true

  '@esbuild/android-arm64@0.21.5':
    optional: true

  '@esbuild/android-arm@0.21.5':
    optional: true

  '@esbuild/android-x64@0.21.5':
    optional: true

  '@esbuild/darwin-arm64@0.21.5':
    optional: true

  '@esbuild/darwin-x64@0.21.5':
    optional: true

  '@esbuild/freebsd-arm64@0.21.5':
    optional: true

  '@esbuild/freebsd-x64@0.21.5':
    optional: true

  '@esbuild/linux-arm64@0.21.5':
    optional: true

  '@esbuild/linux-arm@0.21.5':
    optional: true

  '@esbuild/linux-ia32@0.21.5':
    optional: true

  '@esbuild/linux-loong64@0.21.5':
    optional: true

  '@esbuild/linux-mips64el@0.21.5':
    optional: true

  '@esbuild/linux-ppc64@0.21.5':
    optional: true

  '@esbuild/linux-riscv64@0.21.5':
    optional: true

  '@esbuild/linux-s390x@0.21.5':
    optional: true

  '@esbuild/linux-x64@0.21.5':
    optional: true

  '@esbuild/netbsd-x64@0.21.5':
    optional: true

  '@esbuild/openbsd-x64@0.21.5':
    optional: true

  '@esbuild/sunos-x64@0.21.5':
    optional: true

  '@esbuild/win32-arm64@0.21.5':
    optional: true

  '@esbuild/win32-ia32@0.21.5':
    optional: true

  '@esbuild/win32-x64@0.21.5':
    optional: true

  '@lezer/common@1.2.3': {}

  '@lezer/css@1.3.0':
    dependencies:
      '@lezer/common': 1.2.3
      '@lezer/highlight': 1.2.1
      '@lezer/lr': 1.4.2

  '@lezer/highlight@1.2.1':
    dependencies:
      '@lezer/common': 1.2.3

  '@lezer/html@1.3.10':
    dependencies:
      '@lezer/common': 1.2.3
      '@lezer/highlight': 1.2.1
      '@lezer/lr': 1.4.2

  '@lezer/javascript@1.5.1':
    dependencies:
      '@lezer/common': 1.2.3
      '@lezer/highlight': 1.2.1
      '@lezer/lr': 1.4.2

  '@lezer/lr@1.4.2':
    dependencies:
      '@lezer/common': 1.2.3

  '@lezer/markdown@1.4.3':
    dependencies:
      '@lezer/common': 1.2.3
      '@lezer/highlight': 1.2.1

  '@marijn/find-cluster-break@1.0.2': {}

  '@protobufjs/aspromise@1.1.2': {}

  '@protobufjs/base64@1.1.2': {}

  '@protobufjs/codegen@2.0.4': {}

  '@protobufjs/eventemitter@1.1.0': {}

  '@protobufjs/fetch@1.1.0':
    dependencies:
      '@protobufjs/aspromise': 1.1.2
      '@protobufjs/inquire': 1.1.0

  '@protobufjs/float@1.0.2': {}

  '@protobufjs/inquire@1.1.0': {}

  '@protobufjs/path@1.1.2': {}

  '@protobufjs/pool@1.1.0': {}

  '@protobufjs/utf8@1.1.0': {}

  '@rollup/rollup-android-arm-eabi@4.46.2':
    optional: true

  '@rollup/rollup-android-arm64@4.46.2':
    optional: true

  '@rollup/rollup-darwin-arm64@4.46.2':
    optional: true

  '@rollup/rollup-darwin-x64@4.46.2':
    optional: true

  '@rollup/rollup-freebsd-arm64@4.46.2':
    optional: true

  '@rollup/rollup-freebsd-x64@4.46.2':
    optional: true

  '@rollup/rollup-linux-arm-gnueabihf@4.46.2':
    optional: true

  '@rollup/rollup-linux-arm-musleabihf@4.46.2':
    optional: true

  '@rollup/rollup-linux-arm64-gnu@4.46.2':
    optional: true

  '@rollup/rollup-linux-arm64-musl@4.46.2':
    optional: true

  '@rollup/rollup-linux-loongarch64-gnu@4.46.2':
    optional: true

  '@rollup/rollup-linux-ppc64-gnu@4.46.2':
    optional: true

  '@rollup/rollup-linux-riscv64-gnu@4.46.2':
    optional: true

  '@rollup/rollup-linux-riscv64-musl@4.46.2':
    optional: true

  '@rollup/rollup-linux-s390x-gnu@4.46.2':
    optional: true

  '@rollup/rollup-linux-x64-gnu@4.46.2':
    optional: true

  '@rollup/rollup-linux-x64-musl@4.46.2':
    optional: true

  '@rollup/rollup-win32-arm64-msvc@4.46.2':
    optional: true

  '@rollup/rollup-win32-ia32-msvc@4.46.2':
    optional: true

  '@rollup/rollup-win32-x64-msvc@4.46.2':
    optional: true

  '@smithy/abort-controller@4.0.4':
    dependencies:
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/config-resolver@4.1.4':
    dependencies:
      '@smithy/node-config-provider': 4.1.3
      '@smithy/types': 4.3.1
      '@smithy/util-config-provider': 4.0.0
      '@smithy/util-middleware': 4.0.4
      tslib: 2.8.1

  '@smithy/core@3.7.2':
    dependencies:
      '@smithy/middleware-serde': 4.0.8
      '@smithy/protocol-http': 5.1.2
      '@smithy/types': 4.3.1
      '@smithy/util-base64': 4.0.0
      '@smithy/util-body-length-browser': 4.0.0
      '@smithy/util-middleware': 4.0.4
      '@smithy/util-stream': 4.2.3
      '@smithy/util-utf8': 4.0.0
      tslib: 2.8.1

  '@smithy/credential-provider-imds@4.0.6':
    dependencies:
      '@smithy/node-config-provider': 4.1.3
      '@smithy/property-provider': 4.0.4
      '@smithy/types': 4.3.1
      '@smithy/url-parser': 4.0.4
      tslib: 2.8.1

  '@smithy/eventstream-codec@4.0.4':
    dependencies:
      '@aws-crypto/crc32': 5.2.0
      '@smithy/types': 4.3.1
      '@smithy/util-hex-encoding': 4.0.0
      tslib: 2.8.1

  '@smithy/eventstream-serde-browser@4.0.4':
    dependencies:
      '@smithy/eventstream-serde-universal': 4.0.4
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/eventstream-serde-config-resolver@4.1.2':
    dependencies:
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/eventstream-serde-node@4.0.4':
    dependencies:
      '@smithy/eventstream-serde-universal': 4.0.4
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/eventstream-serde-universal@4.0.4':
    dependencies:
      '@smithy/eventstream-codec': 4.0.4
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/fetch-http-handler@5.1.0':
    dependencies:
      '@smithy/protocol-http': 5.1.2
      '@smithy/querystring-builder': 4.0.4
      '@smithy/types': 4.3.1
      '@smithy/util-base64': 4.0.0
      tslib: 2.8.1

  '@smithy/hash-node@4.0.4':
    dependencies:
      '@smithy/types': 4.3.1
      '@smithy/util-buffer-from': 4.0.0
      '@smithy/util-utf8': 4.0.0
      tslib: 2.8.1

  '@smithy/invalid-dependency@4.0.4':
    dependencies:
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/is-array-buffer@2.2.0':
    dependencies:
      tslib: 2.8.1

  '@smithy/is-array-buffer@4.0.0':
    dependencies:
      tslib: 2.8.1

  '@smithy/middleware-content-length@4.0.4':
    dependencies:
      '@smithy/protocol-http': 5.1.2
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/middleware-endpoint@4.1.17':
    dependencies:
      '@smithy/core': 3.7.2
      '@smithy/middleware-serde': 4.0.8
      '@smithy/node-config-provider': 4.1.3
      '@smithy/shared-ini-file-loader': 4.0.4
      '@smithy/types': 4.3.1
      '@smithy/url-parser': 4.0.4
      '@smithy/util-middleware': 4.0.4
      tslib: 2.8.1

  '@smithy/middleware-retry@4.1.18':
    dependencies:
      '@smithy/node-config-provider': 4.1.3
      '@smithy/protocol-http': 5.1.2
      '@smithy/service-error-classification': 4.0.6
      '@smithy/smithy-client': 4.4.9
      '@smithy/types': 4.3.1
      '@smithy/util-middleware': 4.0.4
      '@smithy/util-retry': 4.0.6
      tslib: 2.8.1
      uuid: 9.0.1

  '@smithy/middleware-serde@4.0.8':
    dependencies:
      '@smithy/protocol-http': 5.1.2
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/middleware-stack@4.0.4':
    dependencies:
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/node-config-provider@4.1.3':
    dependencies:
      '@smithy/property-provider': 4.0.4
      '@smithy/shared-ini-file-loader': 4.0.4
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/node-http-handler@4.1.0':
    dependencies:
      '@smithy/abort-controller': 4.0.4
      '@smithy/protocol-http': 5.1.2
      '@smithy/querystring-builder': 4.0.4
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/property-provider@4.0.4':
    dependencies:
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/protocol-http@5.1.2':
    dependencies:
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/querystring-builder@4.0.4':
    dependencies:
      '@smithy/types': 4.3.1
      '@smithy/util-uri-escape': 4.0.0
      tslib: 2.8.1

  '@smithy/querystring-parser@4.0.4':
    dependencies:
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/service-error-classification@4.0.6':
    dependencies:
      '@smithy/types': 4.3.1

  '@smithy/shared-ini-file-loader@4.0.4':
    dependencies:
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/signature-v4@5.1.2':
    dependencies:
      '@smithy/is-array-buffer': 4.0.0
      '@smithy/protocol-http': 5.1.2
      '@smithy/types': 4.3.1
      '@smithy/util-hex-encoding': 4.0.0
      '@smithy/util-middleware': 4.0.4
      '@smithy/util-uri-escape': 4.0.0
      '@smithy/util-utf8': 4.0.0
      tslib: 2.8.1

  '@smithy/smithy-client@4.4.9':
    dependencies:
      '@smithy/core': 3.7.2
      '@smithy/middleware-endpoint': 4.1.17
      '@smithy/middleware-stack': 4.0.4
      '@smithy/protocol-http': 5.1.2
      '@smithy/types': 4.3.1
      '@smithy/util-stream': 4.2.3
      tslib: 2.8.1

  '@smithy/types@4.3.1':
    dependencies:
      tslib: 2.8.1

  '@smithy/url-parser@4.0.4':
    dependencies:
      '@smithy/querystring-parser': 4.0.4
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/util-base64@4.0.0':
    dependencies:
      '@smithy/util-buffer-from': 4.0.0
      '@smithy/util-utf8': 4.0.0
      tslib: 2.8.1

  '@smithy/util-body-length-browser@4.0.0':
    dependencies:
      tslib: 2.8.1

  '@smithy/util-body-length-node@4.0.0':
    dependencies:
      tslib: 2.8.1

  '@smithy/util-buffer-from@2.2.0':
    dependencies:
      '@smithy/is-array-buffer': 2.2.0
      tslib: 2.8.1

  '@smithy/util-buffer-from@4.0.0':
    dependencies:
      '@smithy/is-array-buffer': 4.0.0
      tslib: 2.8.1

  '@smithy/util-config-provider@4.0.0':
    dependencies:
      tslib: 2.8.1

  '@smithy/util-defaults-mode-browser@4.0.25':
    dependencies:
      '@smithy/property-provider': 4.0.4
      '@smithy/smithy-client': 4.4.9
      '@smithy/types': 4.3.1
      bowser: 2.11.0
      tslib: 2.8.1

  '@smithy/util-defaults-mode-node@4.0.25':
    dependencies:
      '@smithy/config-resolver': 4.1.4
      '@smithy/credential-provider-imds': 4.0.6
      '@smithy/node-config-provider': 4.1.3
      '@smithy/property-provider': 4.0.4
      '@smithy/smithy-client': 4.4.9
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/util-endpoints@3.0.6':
    dependencies:
      '@smithy/node-config-provider': 4.1.3
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/util-hex-encoding@4.0.0':
    dependencies:
      tslib: 2.8.1

  '@smithy/util-middleware@4.0.4':
    dependencies:
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/util-retry@4.0.6':
    dependencies:
      '@smithy/service-error-classification': 4.0.6
      '@smithy/types': 4.3.1
      tslib: 2.8.1

  '@smithy/util-stream@4.2.3':
    dependencies:
      '@smithy/fetch-http-handler': 5.1.0
      '@smithy/node-http-handler': 4.1.0
      '@smithy/types': 4.3.1
      '@smithy/util-base64': 4.0.0
      '@smithy/util-buffer-from': 4.0.0
      '@smithy/util-hex-encoding': 4.0.0
      '@smithy/util-utf8': 4.0.0
      tslib: 2.8.1

  '@smithy/util-uri-escape@4.0.0':
    dependencies:
      tslib: 2.8.1

  '@smithy/util-utf8@2.3.0':
    dependencies:
      '@smithy/util-buffer-from': 2.2.0
      tslib: 2.8.1

  '@smithy/util-utf8@4.0.0':
    dependencies:
      '@smithy/util-buffer-from': 4.0.0
      tslib: 2.8.1

  '@socket.io/component-emitter@3.1.2': {}

  '@symbiotejs/symbiote@1.11.7': {}

  '@types/color-convert@2.0.4':
    dependencies:
      '@types/color-name': 1.1.5

  '@types/color-name@1.1.5': {}

  '@types/debug@4.1.12':
    dependencies:
      '@types/ms': 2.1.0

  '@types/estree@1.0.8': {}

  '@types/hast@3.0.4':
    dependencies:
      '@types/unist': 3.0.3

  '@types/luxon@3.6.2': {}

  '@types/mdast@4.0.4':
    dependencies:
      '@types/unist': 3.0.3

  '@types/ms@2.1.0': {}

  '@types/node@24.1.0':
    dependencies:
      undici-types: 7.8.0

  '@types/unist@3.0.3': {}

  '@types/uuid@9.0.8': {}

  '@ungap/structured-clone@1.3.0': {}

  '@uploadcare/file-uploader@1.16.3-alpha.0':
    dependencies:
      '@symbiotejs/symbiote': 1.11.7
      '@uploadcare/image-shrink': 6.17.0
      '@uploadcare/upload-client': 6.14.3
      keyux: 0.7.2
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate

  '@uploadcare/image-shrink@6.17.0': {}

  '@uploadcare/upload-client@6.14.3':
    dependencies:
      form-data: 4.0.4
      ws: 8.18.3
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate

  asynckit@0.4.0: {}

  bail@2.0.2: {}

  blurhash@2.0.5: {}

  bowser@2.11.0: {}

  call-bind-apply-helpers@1.0.2:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2

  ccount@2.0.1: {}

  character-entities-html4@2.1.0: {}

  character-entities-legacy@3.0.0: {}

  character-entities@2.0.2: {}

  ckeditor5-collaboration@46.0.0:
    dependencies:
      '@ckeditor/ckeditor5-collaboration-core': 46.0.0

  ckeditor5-premium-features@46.0.0(ckeditor5@46.0.0):
    dependencies:
      '@ckeditor/ckeditor5-ai': 46.0.0
      '@ckeditor/ckeditor5-case-change': 46.0.0
      '@ckeditor/ckeditor5-collaboration-core': 46.0.0
      '@ckeditor/ckeditor5-comments': 46.0.0
      '@ckeditor/ckeditor5-document-outline': 46.0.0
      '@ckeditor/ckeditor5-email': 46.0.0
      '@ckeditor/ckeditor5-export-inline-styles': 46.0.0
      '@ckeditor/ckeditor5-export-pdf': 46.0.0
      '@ckeditor/ckeditor5-export-word': 46.0.0
      '@ckeditor/ckeditor5-format-painter': 46.0.0
      '@ckeditor/ckeditor5-import-word': 46.0.0
      '@ckeditor/ckeditor5-line-height': 46.0.0
      '@ckeditor/ckeditor5-list-multi-level': 46.0.0
      '@ckeditor/ckeditor5-merge-fields': 46.0.0
      '@ckeditor/ckeditor5-pagination': 46.0.0
      '@ckeditor/ckeditor5-paste-from-office-enhanced': 46.0.0
      '@ckeditor/ckeditor5-real-time-collaboration': 46.0.0
      '@ckeditor/ckeditor5-revision-history': 46.0.0
      '@ckeditor/ckeditor5-slash-command': 46.0.0
      '@ckeditor/ckeditor5-source-editing-enhanced': 46.0.0
      '@ckeditor/ckeditor5-template': 46.0.0
      '@ckeditor/ckeditor5-track-changes': 46.0.0
      '@ckeditor/ckeditor5-uploadcare': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      ckeditor5: 46.0.0
    transitivePeerDependencies:
      - aws-crt
      - bufferutil
      - supports-color
      - utf-8-validate

  ckeditor5@46.0.0:
    dependencies:
      '@ckeditor/ckeditor5-adapter-ckfinder': 46.0.0
      '@ckeditor/ckeditor5-alignment': 46.0.0
      '@ckeditor/ckeditor5-autoformat': 46.0.0
      '@ckeditor/ckeditor5-autosave': 46.0.0
      '@ckeditor/ckeditor5-basic-styles': 46.0.0
      '@ckeditor/ckeditor5-block-quote': 46.0.0
      '@ckeditor/ckeditor5-bookmark': 46.0.0
      '@ckeditor/ckeditor5-ckbox': 46.0.0
      '@ckeditor/ckeditor5-ckfinder': 46.0.0
      '@ckeditor/ckeditor5-clipboard': 46.0.0
      '@ckeditor/ckeditor5-cloud-services': 46.0.0
      '@ckeditor/ckeditor5-code-block': 46.0.0
      '@ckeditor/ckeditor5-core': 46.0.0
      '@ckeditor/ckeditor5-easy-image': 46.0.0
      '@ckeditor/ckeditor5-editor-balloon': 46.0.0
      '@ckeditor/ckeditor5-editor-classic': 46.0.0
      '@ckeditor/ckeditor5-editor-decoupled': 46.0.0
      '@ckeditor/ckeditor5-editor-inline': 46.0.0
      '@ckeditor/ckeditor5-editor-multi-root': 46.0.0
      '@ckeditor/ckeditor5-emoji': 46.0.0
      '@ckeditor/ckeditor5-engine': 46.0.0
      '@ckeditor/ckeditor5-enter': 46.0.0
      '@ckeditor/ckeditor5-essentials': 46.0.0
      '@ckeditor/ckeditor5-find-and-replace': 46.0.0
      '@ckeditor/ckeditor5-font': 46.0.0
      '@ckeditor/ckeditor5-fullscreen': 46.0.0
      '@ckeditor/ckeditor5-heading': 46.0.0
      '@ckeditor/ckeditor5-highlight': 46.0.0
      '@ckeditor/ckeditor5-horizontal-line': 46.0.0
      '@ckeditor/ckeditor5-html-embed': 46.0.0
      '@ckeditor/ckeditor5-html-support': 46.0.0
      '@ckeditor/ckeditor5-icons': 46.0.0
      '@ckeditor/ckeditor5-image': 46.0.0
      '@ckeditor/ckeditor5-indent': 46.0.0
      '@ckeditor/ckeditor5-language': 46.0.0
      '@ckeditor/ckeditor5-link': 46.0.0
      '@ckeditor/ckeditor5-list': 46.0.0
      '@ckeditor/ckeditor5-markdown-gfm': 46.0.0
      '@ckeditor/ckeditor5-media-embed': 46.0.0
      '@ckeditor/ckeditor5-mention': 46.0.0
      '@ckeditor/ckeditor5-minimap': 46.0.0
      '@ckeditor/ckeditor5-page-break': 46.0.0
      '@ckeditor/ckeditor5-paragraph': 46.0.0
      '@ckeditor/ckeditor5-paste-from-office': 46.0.0
      '@ckeditor/ckeditor5-remove-format': 46.0.0
      '@ckeditor/ckeditor5-restricted-editing': 46.0.0
      '@ckeditor/ckeditor5-select-all': 46.0.0
      '@ckeditor/ckeditor5-show-blocks': 46.0.0
      '@ckeditor/ckeditor5-source-editing': 46.0.0
      '@ckeditor/ckeditor5-special-characters': 46.0.0
      '@ckeditor/ckeditor5-style': 46.0.0
      '@ckeditor/ckeditor5-table': 46.0.0
      '@ckeditor/ckeditor5-theme-lark': 46.0.0
      '@ckeditor/ckeditor5-typing': 46.0.0
      '@ckeditor/ckeditor5-ui': 46.0.0
      '@ckeditor/ckeditor5-undo': 46.0.0
      '@ckeditor/ckeditor5-upload': 46.0.0
      '@ckeditor/ckeditor5-utils': 46.0.0
      '@ckeditor/ckeditor5-watchdog': 46.0.0
      '@ckeditor/ckeditor5-widget': 46.0.0
      '@ckeditor/ckeditor5-word-count': 46.0.0
    transitivePeerDependencies:
      - supports-color

  color-convert@3.1.0:
    dependencies:
      color-name: 2.0.0

  color-name@2.0.0: {}

  color-parse@2.0.2:
    dependencies:
      color-name: 2.0.0

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  comma-separated-tokens@2.0.3: {}

  crelt@1.0.6: {}

  debug@4.3.7:
    dependencies:
      ms: 2.1.3

  debug@4.4.1:
    dependencies:
      ms: 2.1.3

  decode-named-character-reference@1.2.0:
    dependencies:
      character-entities: 2.0.2

  delayed-stream@1.0.0: {}

  dequal@2.0.3: {}

  devlop@1.1.0:
    dependencies:
      dequal: 2.0.3

  dunder-proto@1.0.1:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-errors: 1.3.0
      gopd: 1.2.0

  engine.io-client@6.5.4:
    dependencies:
      '@socket.io/component-emitter': 3.1.2
      debug: 4.3.7
      engine.io-parser: 5.2.3
      ws: 8.17.1
      xmlhttprequest-ssl: 2.0.0
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  engine.io-parser@5.2.3: {}

  es-define-property@1.0.1: {}

  es-errors@1.3.0: {}

  es-object-atoms@1.1.1:
    dependencies:
      es-errors: 1.3.0

  es-set-tostringtag@2.1.0:
    dependencies:
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  es-toolkit@1.39.5: {}

  esbuild@0.21.5:
    optionalDependencies:
      '@esbuild/aix-ppc64': 0.21.5
      '@esbuild/android-arm': 0.21.5
      '@esbuild/android-arm64': 0.21.5
      '@esbuild/android-x64': 0.21.5
      '@esbuild/darwin-arm64': 0.21.5
      '@esbuild/darwin-x64': 0.21.5
      '@esbuild/freebsd-arm64': 0.21.5
      '@esbuild/freebsd-x64': 0.21.5
      '@esbuild/linux-arm': 0.21.5
      '@esbuild/linux-arm64': 0.21.5
      '@esbuild/linux-ia32': 0.21.5
      '@esbuild/linux-loong64': 0.21.5
      '@esbuild/linux-mips64el': 0.21.5
      '@esbuild/linux-ppc64': 0.21.5
      '@esbuild/linux-riscv64': 0.21.5
      '@esbuild/linux-s390x': 0.21.5
      '@esbuild/linux-x64': 0.21.5
      '@esbuild/netbsd-x64': 0.21.5
      '@esbuild/openbsd-x64': 0.21.5
      '@esbuild/sunos-x64': 0.21.5
      '@esbuild/win32-arm64': 0.21.5
      '@esbuild/win32-ia32': 0.21.5
      '@esbuild/win32-x64': 0.21.5

  escape-string-regexp@5.0.0: {}

  extend@3.0.2: {}

  fast-xml-parser@4.4.1:
    dependencies:
      strnum: 1.1.2

  form-data@4.0.4:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      es-set-tostringtag: 2.1.0
      hasown: 2.0.2
      mime-types: 2.1.35

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  fuzzysort@3.1.0: {}

  get-intrinsic@1.3.0:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      function-bind: 1.1.2
      get-proto: 1.0.1
      gopd: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      math-intrinsics: 1.1.0

  get-proto@1.0.1:
    dependencies:
      dunder-proto: 1.0.1
      es-object-atoms: 1.1.1

  gopd@1.2.0: {}

  has-symbols@1.1.0: {}

  has-tostringtag@1.0.2:
    dependencies:
      has-symbols: 1.1.0

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  hast-util-embedded@3.0.0:
    dependencies:
      '@types/hast': 3.0.4
      hast-util-is-element: 3.0.0

  hast-util-from-dom@5.0.1:
    dependencies:
      '@types/hast': 3.0.4
      hastscript: 9.0.1
      web-namespaces: 2.0.1

  hast-util-has-property@3.0.0:
    dependencies:
      '@types/hast': 3.0.4

  hast-util-is-body-ok-link@3.0.1:
    dependencies:
      '@types/hast': 3.0.4

  hast-util-is-element@3.0.0:
    dependencies:
      '@types/hast': 3.0.4

  hast-util-minify-whitespace@1.0.1:
    dependencies:
      '@types/hast': 3.0.4
      hast-util-embedded: 3.0.0
      hast-util-is-element: 3.0.0
      hast-util-whitespace: 3.0.0
      unist-util-is: 6.0.0

  hast-util-parse-selector@4.0.0:
    dependencies:
      '@types/hast': 3.0.4

  hast-util-phrasing@3.0.1:
    dependencies:
      '@types/hast': 3.0.4
      hast-util-embedded: 3.0.0
      hast-util-has-property: 3.0.0
      hast-util-is-body-ok-link: 3.0.1
      hast-util-is-element: 3.0.0

  hast-util-to-dom@4.0.1:
    dependencies:
      '@types/hast': 3.0.4
      property-information: 7.1.0
      web-namespaces: 2.0.1

  hast-util-to-html@9.0.5:
    dependencies:
      '@types/hast': 3.0.4
      '@types/unist': 3.0.3
      ccount: 2.0.1
      comma-separated-tokens: 2.0.3
      hast-util-whitespace: 3.0.0
      html-void-elements: 3.0.0
      mdast-util-to-hast: 13.2.0
      property-information: 7.1.0
      space-separated-tokens: 2.0.2
      stringify-entities: 4.0.4
      zwitch: 2.0.4

  hast-util-to-mdast@10.1.2:
    dependencies:
      '@types/hast': 3.0.4
      '@types/mdast': 4.0.4
      '@ungap/structured-clone': 1.3.0
      hast-util-phrasing: 3.0.1
      hast-util-to-html: 9.0.5
      hast-util-to-text: 4.0.2
      hast-util-whitespace: 3.0.0
      mdast-util-phrasing: 4.1.0
      mdast-util-to-hast: 13.2.0
      mdast-util-to-string: 4.0.0
      rehype-minify-whitespace: 6.0.2
      trim-trailing-lines: 2.1.0
      unist-util-position: 5.0.0
      unist-util-visit: 5.0.0

  hast-util-to-text@4.0.2:
    dependencies:
      '@types/hast': 3.0.4
      '@types/unist': 3.0.3
      hast-util-is-element: 3.0.0
      unist-util-find-after: 5.0.0

  hast-util-whitespace@3.0.0:
    dependencies:
      '@types/hast': 3.0.4

  hastscript@9.0.1:
    dependencies:
      '@types/hast': 3.0.4
      comma-separated-tokens: 2.0.3
      hast-util-parse-selector: 4.0.0
      property-information: 7.1.0
      space-separated-tokens: 2.0.2

  html-void-elements@3.0.0: {}

  is-plain-obj@4.1.0: {}

  keyux@0.7.2: {}

  long@5.3.2: {}

  longest-streak@3.1.0: {}

  luxon@3.6.1: {}

  markdown-table@3.0.4: {}

  math-intrinsics@1.1.0: {}

  mdast-util-find-and-replace@3.0.2:
    dependencies:
      '@types/mdast': 4.0.4
      escape-string-regexp: 5.0.0
      unist-util-is: 6.0.0
      unist-util-visit-parents: 6.0.1

  mdast-util-from-markdown@2.0.2:
    dependencies:
      '@types/mdast': 4.0.4
      '@types/unist': 3.0.3
      decode-named-character-reference: 1.2.0
      devlop: 1.1.0
      mdast-util-to-string: 4.0.0
      micromark: 4.0.2
      micromark-util-decode-numeric-character-reference: 2.0.2
      micromark-util-decode-string: 2.0.1
      micromark-util-normalize-identifier: 2.0.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2
      unist-util-stringify-position: 4.0.0
    transitivePeerDependencies:
      - supports-color

  mdast-util-gfm-autolink-literal@2.0.1:
    dependencies:
      '@types/mdast': 4.0.4
      ccount: 2.0.1
      devlop: 1.1.0
      mdast-util-find-and-replace: 3.0.2
      micromark-util-character: 2.1.1

  mdast-util-gfm-footnote@2.1.0:
    dependencies:
      '@types/mdast': 4.0.4
      devlop: 1.1.0
      mdast-util-from-markdown: 2.0.2
      mdast-util-to-markdown: 2.1.2
      micromark-util-normalize-identifier: 2.0.1
    transitivePeerDependencies:
      - supports-color

  mdast-util-gfm-strikethrough@2.0.0:
    dependencies:
      '@types/mdast': 4.0.4
      mdast-util-from-markdown: 2.0.2
      mdast-util-to-markdown: 2.1.2
    transitivePeerDependencies:
      - supports-color

  mdast-util-gfm-table@2.0.0:
    dependencies:
      '@types/mdast': 4.0.4
      devlop: 1.1.0
      markdown-table: 3.0.4
      mdast-util-from-markdown: 2.0.2
      mdast-util-to-markdown: 2.1.2
    transitivePeerDependencies:
      - supports-color

  mdast-util-gfm-task-list-item@2.0.0:
    dependencies:
      '@types/mdast': 4.0.4
      devlop: 1.1.0
      mdast-util-from-markdown: 2.0.2
      mdast-util-to-markdown: 2.1.2
    transitivePeerDependencies:
      - supports-color

  mdast-util-gfm@3.1.0:
    dependencies:
      mdast-util-from-markdown: 2.0.2
      mdast-util-gfm-autolink-literal: 2.0.1
      mdast-util-gfm-footnote: 2.1.0
      mdast-util-gfm-strikethrough: 2.0.0
      mdast-util-gfm-table: 2.0.0
      mdast-util-gfm-task-list-item: 2.0.0
      mdast-util-to-markdown: 2.1.2
    transitivePeerDependencies:
      - supports-color

  mdast-util-newline-to-break@2.0.0:
    dependencies:
      '@types/mdast': 4.0.4
      mdast-util-find-and-replace: 3.0.2

  mdast-util-phrasing@4.1.0:
    dependencies:
      '@types/mdast': 4.0.4
      unist-util-is: 6.0.0

  mdast-util-to-hast@13.2.0:
    dependencies:
      '@types/hast': 3.0.4
      '@types/mdast': 4.0.4
      '@ungap/structured-clone': 1.3.0
      devlop: 1.1.0
      micromark-util-sanitize-uri: 2.0.1
      trim-lines: 3.0.1
      unist-util-position: 5.0.0
      unist-util-visit: 5.0.0
      vfile: 6.0.3

  mdast-util-to-markdown@2.1.2:
    dependencies:
      '@types/mdast': 4.0.4
      '@types/unist': 3.0.3
      longest-streak: 3.1.0
      mdast-util-phrasing: 4.1.0
      mdast-util-to-string: 4.0.0
      micromark-util-classify-character: 2.0.1
      micromark-util-decode-string: 2.0.1
      unist-util-visit: 5.0.0
      zwitch: 2.0.4

  mdast-util-to-string@4.0.0:
    dependencies:
      '@types/mdast': 4.0.4

  micromark-core-commonmark@2.0.3:
    dependencies:
      decode-named-character-reference: 1.2.0
      devlop: 1.1.0
      micromark-factory-destination: 2.0.1
      micromark-factory-label: 2.0.1
      micromark-factory-space: 2.0.1
      micromark-factory-title: 2.0.1
      micromark-factory-whitespace: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-chunked: 2.0.1
      micromark-util-classify-character: 2.0.1
      micromark-util-html-tag-name: 2.0.1
      micromark-util-normalize-identifier: 2.0.1
      micromark-util-resolve-all: 2.0.1
      micromark-util-subtokenize: 2.1.0
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-extension-gfm-autolink-literal@2.1.0:
    dependencies:
      micromark-util-character: 2.1.1
      micromark-util-sanitize-uri: 2.0.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-extension-gfm-footnote@2.1.0:
    dependencies:
      devlop: 1.1.0
      micromark-core-commonmark: 2.0.3
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-normalize-identifier: 2.0.1
      micromark-util-sanitize-uri: 2.0.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-extension-gfm-strikethrough@2.1.0:
    dependencies:
      devlop: 1.1.0
      micromark-util-chunked: 2.0.1
      micromark-util-classify-character: 2.0.1
      micromark-util-resolve-all: 2.0.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-extension-gfm-table@2.1.1:
    dependencies:
      devlop: 1.1.0
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-extension-gfm-tagfilter@2.0.0:
    dependencies:
      micromark-util-types: 2.0.2

  micromark-extension-gfm-task-list-item@2.1.0:
    dependencies:
      devlop: 1.1.0
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-extension-gfm@3.0.0:
    dependencies:
      micromark-extension-gfm-autolink-literal: 2.1.0
      micromark-extension-gfm-footnote: 2.1.0
      micromark-extension-gfm-strikethrough: 2.1.0
      micromark-extension-gfm-table: 2.1.1
      micromark-extension-gfm-tagfilter: 2.0.0
      micromark-extension-gfm-task-list-item: 2.1.0
      micromark-util-combine-extensions: 2.0.1
      micromark-util-types: 2.0.2

  micromark-factory-destination@2.0.1:
    dependencies:
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-factory-label@2.0.1:
    dependencies:
      devlop: 1.1.0
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-factory-space@2.0.1:
    dependencies:
      micromark-util-character: 2.1.1
      micromark-util-types: 2.0.2

  micromark-factory-title@2.0.1:
    dependencies:
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-factory-whitespace@2.0.1:
    dependencies:
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-util-character@2.1.1:
    dependencies:
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-util-chunked@2.0.1:
    dependencies:
      micromark-util-symbol: 2.0.1

  micromark-util-classify-character@2.0.1:
    dependencies:
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-util-combine-extensions@2.0.1:
    dependencies:
      micromark-util-chunked: 2.0.1
      micromark-util-types: 2.0.2

  micromark-util-decode-numeric-character-reference@2.0.2:
    dependencies:
      micromark-util-symbol: 2.0.1

  micromark-util-decode-string@2.0.1:
    dependencies:
      decode-named-character-reference: 1.2.0
      micromark-util-character: 2.1.1
      micromark-util-decode-numeric-character-reference: 2.0.2
      micromark-util-symbol: 2.0.1

  micromark-util-encode@2.0.1: {}

  micromark-util-html-tag-name@2.0.1: {}

  micromark-util-normalize-identifier@2.0.1:
    dependencies:
      micromark-util-symbol: 2.0.1

  micromark-util-resolve-all@2.0.1:
    dependencies:
      micromark-util-types: 2.0.2

  micromark-util-sanitize-uri@2.0.1:
    dependencies:
      micromark-util-character: 2.1.1
      micromark-util-encode: 2.0.1
      micromark-util-symbol: 2.0.1

  micromark-util-subtokenize@2.1.0:
    dependencies:
      devlop: 1.1.0
      micromark-util-chunked: 2.0.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-util-symbol@2.0.1: {}

  micromark-util-types@2.0.2: {}

  micromark@4.0.2:
    dependencies:
      '@types/debug': 4.1.12
      debug: 4.4.1
      decode-named-character-reference: 1.2.0
      devlop: 1.1.0
      micromark-core-commonmark: 2.0.3
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-chunked: 2.0.1
      micromark-util-combine-extensions: 2.0.1
      micromark-util-decode-numeric-character-reference: 2.0.2
      micromark-util-encode: 2.0.1
      micromark-util-normalize-identifier: 2.0.1
      micromark-util-resolve-all: 2.0.1
      micromark-util-sanitize-uri: 2.0.1
      micromark-util-subtokenize: 2.1.0
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2
    transitivePeerDependencies:
      - supports-color

  mime-db@1.52.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  ms@2.1.3: {}

  nanoid@3.3.11: {}

  picocolors@1.1.1: {}

  postcss@8.5.6:
    dependencies:
      nanoid: 3.3.11
      picocolors: 1.1.1
      source-map-js: 1.2.1

  property-information@7.1.0: {}

  protobufjs@7.5.0:
    dependencies:
      '@protobufjs/aspromise': 1.1.2
      '@protobufjs/base64': 1.1.2
      '@protobufjs/codegen': 2.0.4
      '@protobufjs/eventemitter': 1.1.0
      '@protobufjs/fetch': 1.1.0
      '@protobufjs/float': 1.0.2
      '@protobufjs/inquire': 1.1.0
      '@protobufjs/path': 1.1.2
      '@protobufjs/pool': 1.1.0
      '@protobufjs/utf8': 1.1.0
      '@types/node': 24.1.0
      long: 5.3.2

  querystringify@2.2.0: {}

  rehype-dom-parse@5.0.2:
    dependencies:
      '@types/hast': 3.0.4
      hast-util-from-dom: 5.0.1
      unified: 11.0.5

  rehype-dom-stringify@4.0.2:
    dependencies:
      '@types/hast': 3.0.4
      hast-util-to-dom: 4.0.1
      unified: 11.0.5

  rehype-minify-whitespace@6.0.2:
    dependencies:
      '@types/hast': 3.0.4
      hast-util-minify-whitespace: 1.0.1

  rehype-remark@10.0.1:
    dependencies:
      '@types/hast': 3.0.4
      '@types/mdast': 4.0.4
      hast-util-to-mdast: 10.1.2
      unified: 11.0.5
      vfile: 6.0.3

  remark-breaks@4.0.0:
    dependencies:
      '@types/mdast': 4.0.4
      mdast-util-newline-to-break: 2.0.0
      unified: 11.0.5

  remark-gfm@4.0.1:
    dependencies:
      '@types/mdast': 4.0.4
      mdast-util-gfm: 3.1.0
      micromark-extension-gfm: 3.0.0
      remark-parse: 11.0.0
      remark-stringify: 11.0.0
      unified: 11.0.5
    transitivePeerDependencies:
      - supports-color

  remark-parse@11.0.0:
    dependencies:
      '@types/mdast': 4.0.4
      mdast-util-from-markdown: 2.0.2
      micromark-util-types: 2.0.2
      unified: 11.0.5
    transitivePeerDependencies:
      - supports-color

  remark-rehype@11.1.2:
    dependencies:
      '@types/hast': 3.0.4
      '@types/mdast': 4.0.4
      mdast-util-to-hast: 13.2.0
      unified: 11.0.5
      vfile: 6.0.3

  remark-stringify@11.0.0:
    dependencies:
      '@types/mdast': 4.0.4
      mdast-util-to-markdown: 2.1.2
      unified: 11.0.5

  requires-port@1.0.0: {}

  rollup@4.46.2:
    dependencies:
      '@types/estree': 1.0.8
    optionalDependencies:
      '@rollup/rollup-android-arm-eabi': 4.46.2
      '@rollup/rollup-android-arm64': 4.46.2
      '@rollup/rollup-darwin-arm64': 4.46.2
      '@rollup/rollup-darwin-x64': 4.46.2
      '@rollup/rollup-freebsd-arm64': 4.46.2
      '@rollup/rollup-freebsd-x64': 4.46.2
      '@rollup/rollup-linux-arm-gnueabihf': 4.46.2
      '@rollup/rollup-linux-arm-musleabihf': 4.46.2
      '@rollup/rollup-linux-arm64-gnu': 4.46.2
      '@rollup/rollup-linux-arm64-musl': 4.46.2
      '@rollup/rollup-linux-loongarch64-gnu': 4.46.2
      '@rollup/rollup-linux-ppc64-gnu': 4.46.2
      '@rollup/rollup-linux-riscv64-gnu': 4.46.2
      '@rollup/rollup-linux-riscv64-musl': 4.46.2
      '@rollup/rollup-linux-s390x-gnu': 4.46.2
      '@rollup/rollup-linux-x64-gnu': 4.46.2
      '@rollup/rollup-linux-x64-musl': 4.46.2
      '@rollup/rollup-win32-arm64-msvc': 4.46.2
      '@rollup/rollup-win32-ia32-msvc': 4.46.2
      '@rollup/rollup-win32-x64-msvc': 4.46.2
      fsevents: 2.3.3

  socket.io-client@4.7.0:
    dependencies:
      '@socket.io/component-emitter': 3.1.2
      debug: 4.3.7
      engine.io-client: 6.5.4
      socket.io-parser: 4.2.4
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  socket.io-parser@4.2.4:
    dependencies:
      '@socket.io/component-emitter': 3.1.2
      debug: 4.3.7
    transitivePeerDependencies:
      - supports-color

  source-map-js@1.2.1: {}

  space-separated-tokens@2.0.2: {}

  specificity@0.4.1: {}

  stringify-entities@4.0.4:
    dependencies:
      character-entities-html4: 2.1.0
      character-entities-legacy: 3.0.0

  strnum@1.1.2: {}

  style-mod@4.1.2: {}

  trim-lines@3.0.1: {}

  trim-trailing-lines@2.1.0: {}

  trough@2.2.0: {}

  tslib@2.8.1: {}

  undici-types@7.8.0: {}

  unified@11.0.5:
    dependencies:
      '@types/unist': 3.0.3
      bail: 2.0.2
      devlop: 1.1.0
      extend: 3.0.2
      is-plain-obj: 4.1.0
      trough: 2.2.0
      vfile: 6.0.3

  unist-util-find-after@5.0.0:
    dependencies:
      '@types/unist': 3.0.3
      unist-util-is: 6.0.0

  unist-util-is@6.0.0:
    dependencies:
      '@types/unist': 3.0.3

  unist-util-position@5.0.0:
    dependencies:
      '@types/unist': 3.0.3

  unist-util-stringify-position@4.0.0:
    dependencies:
      '@types/unist': 3.0.3

  unist-util-visit-parents@6.0.1:
    dependencies:
      '@types/unist': 3.0.3
      unist-util-is: 6.0.0

  unist-util-visit@5.0.0:
    dependencies:
      '@types/unist': 3.0.3
      unist-util-is: 6.0.0
      unist-util-visit-parents: 6.0.1

  url-parse@1.5.10:
    dependencies:
      querystringify: 2.2.0
      requires-port: 1.0.0

  uuid@9.0.1: {}

  vanilla-colorful@0.7.2: {}

  vfile-message@4.0.3:
    dependencies:
      '@types/unist': 3.0.3
      unist-util-stringify-position: 4.0.0

  vfile@6.0.3:
    dependencies:
      '@types/unist': 3.0.3
      vfile-message: 4.0.3

  vite@5.4.19(@types/node@24.1.0):
    dependencies:
      esbuild: 0.21.5
      postcss: 8.5.6
      rollup: 4.46.2
    optionalDependencies:
      '@types/node': 24.1.0
      fsevents: 2.3.3

  w3c-keyname@2.2.8: {}

  web-namespaces@2.0.1: {}

  ws@8.17.1: {}

  ws@8.18.3: {}

  xmlhttprequest-ssl@2.0.0: {}

  zwitch@2.0.4: {}
