/**
 * CKEditor 5 requires a license key.
 *
 * The "GPL" license key used below only allows you to use the open-source features.
 * To use the premium features, replace it with your commercial license key.
 * If you don't have one, you can get a trial license key from https://portal.ckeditor.com/checkout?plan=free.
 */
const LICENSE_KEY = 'GPL';

if ( LICENSE_KEY === 'GPL' ) {
	alert( 'Premium features are disabled, because they require a commercial license key. Check the index.js file for more information.' );
}

import {
  Alignment,
  AutoImage,
  Autoformat,
  Autosave,
  BlockQuote,
  Bold,
  Essentials,
  FontBackgroundColor,
  FontColor,
  FontFamily,
  FontSize,
  Heading,
  ImageBlock,
  ImageCaption,
  ImageInline,
  ImageInsert,
  ImageInsertViaUrl,
  ImageResize,
  ImageStyle,
  ImageTextAlternative,
  ImageToolbar,
  ImageUpload,
  Indent,
  IndentBlock,
  InlineEditor,
  Italic,
  Link,
  LinkImage,
  List,
  ListProperties,
  Markdown,
  MediaEmbed,
  Mention,
  Paragraph,
  PasteFromMarkdownExperimental,
  PasteFromOffice,
  SimpleUploadAdapter,
  SpecialCharacters,
  SpecialCharactersArrows,
  SpecialCharactersCurrency,
  SpecialCharactersEssentials,
  SpecialCharactersLatin,
  SpecialCharactersMathematical,
  SpecialCharactersText,
  Strikethrough,
  Subscript,
  Superscript,
  Table,
  TableCaption,
  TableCellProperties,
  TableColumnResize,
  // TableLayout,
  TableProperties,
  TableToolbar,
  TextTransformation,
  TodoList,
  Underline,
} from 'ckeditor5';

import {
	SlashCommand
} from 'ckeditor5-premium-features';

import 'ckeditor5/ckeditor5.css';
import 'ckeditor5-premium-features/ckeditor5-premium-features.css';

const defaultConfig = {
    plugins: [
      // Mathlive,
      // MathlivePanelview,
      Alignment,
      Autoformat,
      AutoImage,
      Autosave,
      // BlockQuote,
      Bold,
      Essentials,
      FontBackgroundColor,
      FontColor,
      FontFamily,
      FontSize,
      Heading,
      ImageBlock,
      ImageCaption,
      ImageInline,
      ImageInsert,
      ImageInsertViaUrl,
      ImageResize,
      ImageStyle,
      ImageTextAlternative,
      ImageToolbar,
      ImageUpload,
      Indent,
      // IndentBlock,
      Italic,
      // Link,
      // LinkImage,
      // List,
      // ListProperties,
      // Markdown,
      MediaEmbed,
      Mention,
      Paragraph,
      PasteFromMarkdownExperimental,
      PasteFromOffice,
      SimpleUploadAdapter,
      SpecialCharacters,
      SpecialCharactersArrows,
      SpecialCharactersCurrency,
      SpecialCharactersEssentials,
      SpecialCharactersLatin,
      SpecialCharactersMathematical,
      SpecialCharactersText,
      Strikethrough,
      Subscript,
      Superscript,
      Table,
      TableCaption,
      TableCellProperties,
      TableColumnResize,
      TableProperties,
      TableToolbar,
      TextTransformation,
      TodoList,
      Underline,
    ],
	licenseKey: LICENSE_KEY,
    toolbar: {
      items: [
        'fontFamily',
        'fontSize',
        'fontColor',
        'fontBackgroundColor',
        '|',
        'bold',
        'italic',
        'underline',
        'strikethrough',
        'subscript',
        'superscript',
        '|',
        'alignment',
        'outdent',
        'indent',
        '|',
        'insertImage',
        'insertTable',
        'specialCharacters',
        'mathlive',
      ],
      shouldNotGroupWhenFull: true,
      removeItems: [], // 可以在这里移除不需要的工具
    },
	heading: {
		options: [
			{ model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
			{
				model: 'heading1',
				view: 'h1',
				title: 'Heading 1',
				class: 'ck-heading_heading1'
			},
			{
				model: 'heading2',
				view: 'h2',
				title: 'Heading 2',
				class: 'ck-heading_heading2'
			},
			{
				model: 'heading3',
				view: 'h3',
				title: 'Heading 3',
				class: 'ck-heading_heading3'
			},
			{
				model: 'heading4',
				view: 'h4',
				title: 'Heading 4',
				class: 'ck-heading_heading4'
			}
		]
	},
	image: {
		toolbar: [
			'imageStyle:inline',
			'imageStyle:block',
			'imageStyle:side',
			'|',
			'toggleImageCaption',
			'imageTextAlternative'
		]
	},
	link: {
		addTargetToExternalLinks: true,
		defaultProtocol: 'https://'
	},
	table: {
		contentToolbar: [
			'tableColumn',
			'tableRow',
			'mergeTableCells',
			'tableProperties',
			'tableCellProperties'
		]
	}
};

const headerConfig = {
	plugins: [ Essentials, Autoformat, Bold, Italic, Heading, Link, Paragraph ],
	toolbar: [ 'heading', '|', 'bold', 'italic', 'link' ],
	heading: {
		options: [
			{ model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
			{
				model: 'heading1',
				view: 'h1',
				title: 'Heading 1',
				class: 'ck-heading_heading1'
			},
			{
				model: 'heading2',
				view: 'h2',
				title: 'Heading 2',
				class: 'ck-heading_heading2'
			},
			{
				model: 'heading3',
				view: 'h3',
				title: 'Heading 3',
				class: 'ck-heading_heading3'
			},
			{
				model: 'heading4',
				view: 'h4',
				title: 'Heading 4',
				class: 'ck-heading_heading4'
			}
		]
	},
	link: {
		addTargetToExternalLinks: true,
		defaultProtocol: 'https://'
	}
};

const inlineElementsIds = [
	'inline-header',
	'inline-main',
	'inline-footer-first',
	'inline-footer-second'
];

// 存储所有编辑器实例
const editors = {};

inlineElementsIds.forEach( id => {
	const element = document.getElementById( id );

	if ( !element ) {
		return;
	}

	InlineEditor.create(
		element,
		id === 'inline-header' ? headerConfig : defaultConfig
	)
		.then( editor => {
			console.log( editor );
			// 将编辑器实例存储到对象中
			editors[id] = editor;
			// 保持向后兼容性，将最后一个编辑器设为全局变量
			window.editor = editor;
		} )
		.catch( error => {
			console.error( error.stack );
		} );
} );

// 添加获取内容的功能
document.addEventListener('DOMContentLoaded', function() {
	// 等待一段时间确保编辑器都已初始化
	setTimeout(() => {
		const getContentBtn = document.getElementById('get-content-btn');
		const contentOutput = document.getElementById('content-output');

		if (getContentBtn && contentOutput) {
			getContentBtn.addEventListener('click', function() {
				let allContent = '';

				// 遍历所有编辑器实例，获取内容
				inlineElementsIds.forEach(id => {
					const editor = editors[id];
					if (editor) {
						let content = editor.getData();
						console.log('原始内容:', content);

						// 为表格添加CSS类以确保样式正确显示
						content = content.replace(/<table/g, '<table class="table-bordered table-hover"');

						console.log('处理后内容:', content);

						if (content.trim()) {
							allContent += `<div style="margin-bottom: 20px; padding: 10px; border-left: 3px solid #007cba;">
								<h5 style="margin: 0 0 10px 0; color: #007cba;">来自 ${id}:</h5>
								${content}
							</div>`;
						}
					}
				});

				if (allContent) {
					contentOutput.innerHTML = allContent;
				} else {
					contentOutput.innerHTML = '<p style="color: #666;">编辑器中暂无内容</p>';
				}
			});
		}
	}, 1000); // 等待1秒确保编辑器初始化完成
});
