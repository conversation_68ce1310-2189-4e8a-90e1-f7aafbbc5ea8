# CKEditor 5 demos

## Contents

This repository holds the source code for CKEditor 5 demos that can be found at the [CKEditor 5 website](https://ckeditor.com/ckeditor-5/demo).

### Features examples

- [AI Assistant](ai-assistant)
- [Feature-rich editor](feature-rich)
- [Merge fields editor](merge-fields)
- [Editor with internationalization](internationalization)
- [Markdown editor](markdown)
- [Editor with "MathType"](mathtype)
- [Editor with "Paste from Office enhanced"](paste-from-office-enhanced)
- [Productivity Pack](productivity-pack)
- [Editor with source code editing and HTML support](source-code-editing)
- [Editor with "WProofreader"](wproofreader)
- [Editor with "Uploadcare"](image-optimizer)

### User interface (UI) examples

- [Headless](headless)
- [Mobile-friendly editor](mobile)
- [Editor with Balloon user interface](user-interface-balloon)
- [Editor with Balloon Block user interface](user-interface-balloon-block)
- [Editor with Bottom Toolbar user interface](user-interface-bottom-toolbar)
- [Editor with <PERSON>ton Grouping user interface](user-interface-button-grouping)
- [Editor with Classic user interface](user-interface-classic)
- [Editor with Document user interface](user-interface-document)
- [Editor with Inline user interface](user-interface-inline)

## Usage

Each demo is held in an independent directory. Steps to run them can be found in the respective `README.md` files. See [Feature-rich README.md](feature-rich#readme) as an example.

## Development

To install dependencies for all demos and run a simple test checking if all demos can be built and run, run the following commands in the root directory:

```bash
yarn install
bash build-and-test-demos.sh
```
