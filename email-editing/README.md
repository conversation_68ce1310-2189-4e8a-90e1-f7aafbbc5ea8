# Editor with Email Editing

This demo showcases an editor configured for email content creation. It allows you to easily compose and edit email messages with all the rich-text formatting capabilities. The editor ensures that all styling is exported as inline CSS to display your emails consistently across various email clients. You can format text, add images, and structure your email content professionally while maintaining compatibility with email delivery standards.

See this demo live at [ckeditor.com](https://ckeditor.com/ckeditor-5/demo/email-editing ) or read more about it in the [CKEditor 5 documentation](https://ckeditor.com/docs/ckeditor5/latest/features/email-editing/email.html).

## Installation steps

1. Clone this repository:

```shell
<NAME_EMAIL>:ckeditor/ckeditor5-demos.git
```

2. Change the directory and install the dependencies:

```shell
cd ckeditor5-demos/email-editing && yarn
```

3. Start the demo:

```shell
yarn dev
```
